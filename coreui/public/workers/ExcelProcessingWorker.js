/**
 * Web Worker for processing large Excel datasets
 * Handles data transformation and chunked processing to prevent main thread blocking
 */

// Import ExcelJS in worker context
importScripts('https://unpkg.com/exceljs@4.3.0/dist/exceljs.min.js');

let workbook = null;
let worksheet = null;
let processedRows = 0;

self.addEventListener('message', async function(e) {
    const { type, data } = e.data;
    
    try {
        switch (type) {
            case 'INIT_WORKBOOK':
                await initializeWorkbook(data);
                break;
                
            case 'PROCESS_CHUNK':
                await processDataChunk(data);
                break;
                
            case 'FINALIZE_WORKBOOK':
                await finalizeWorkbook(data);
                break;
                
            case 'GENERATE_BUFFER':
                await generateBuffer();
                break;
                
            default:
                throw new Error(`Unknown message type: ${type}`);
        }
    } catch (error) {
        self.postMessage({
            type: 'ERROR',
            error: {
                message: error.message,
                stack: error.stack
            }
        });
    }
});

async function initializeWorkbook({ title, dates, headerItems, subTitle }) {
    workbook = new ExcelJS.Workbook();
    worksheet = workbook.addWorksheet(title);
    processedRows = 0;
    
    // Setup styles
    const headerStyle = {
        font: { bold: true, color: { argb: 'FFFFFF' }, size: 11 },
        fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: '002060' } },
        alignment: { horizontal: 'center', vertical: 'middle' },
    };

    const subHeaderStyle = {
        font: { bold: true, color: { argb: 'FFFFFF' }, size: 10 },
        fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: '4472C4' } },
        alignment: { horizontal: 'center', vertical: 'middle' },
    };

    // Title row
    const titleRow = worksheet.getRow(1);
    titleRow.height = 30;
    const titleCell = titleRow.getCell(1);
    titleCell.value = title;
    titleCell.style = headerStyle;
    worksheet.mergeCells(1, 1, 1, headerItems.length);

    // Subtitle row
    const subTitleRow = worksheet.getRow(2);
    subTitleRow.height = 25;
    const subTitleCell = subTitleRow.getCell(1);
    subTitleCell.value = subTitle;
    subTitleCell.style = subHeaderStyle;
    worksheet.mergeCells(2, 1, 2, headerItems.length);

    // Date headers (rows 4-14)
    if (dates && dates.length > 0) {
        dates.forEach((date, index) => {
            const row = worksheet.getRow(4 + index);
            row.height = 25;
            headerItems.forEach((header, colIndex) => {
                const cell = row.getCell(1 + colIndex);
                cell.value = date[header] || '';
                cell.style = subHeaderStyle;
            });
        });
    }

    // Main headers row (row 15)
    const headerRow = worksheet.getRow(15);
    headerRow.height = 30;
    headerItems.forEach((header, index) => {
        const cell = headerRow.getCell(1 + index);
        cell.value = header;
        cell.style = headerStyle;
    });
    
    self.postMessage({
        type: 'WORKBOOK_INITIALIZED',
        processedRows: 0
    });
}

async function processDataChunk({ chunk, headerItems, title, startRowIndex }) {
    const colorMapping = {
        green: '008000',
        red: 'FF0000',
        blue: '0000FF',
        black: '000000',
    };

    for (let i = 0; i < chunk.length; i++) {
        const rowData = chunk[i];
        const row = worksheet.getRow(startRowIndex + i);
        row.height = 25;

        headerItems.forEach((header, colIndex) => {
            const cell = row.getCell(1 + colIndex);
            cell.value = rowData[header];
            cell.alignment = { horizontal: 'center', vertical: 'middle' };
            cell.font = { bold: true };

            // Apply styling based on title and data
            if (rowData.color) {
                applyEmployeeColoring(header, rowData, cell, colorMapping);
            }
            if (title === 'Overall Visits Report') {
                applyOverallVisitsStyle(header, rowData, cell);
            }
            if (title === 'Employee Tracking Report') {
                applyEmployeeTrackingStyle(cell);
            }
            if (title === 'Structure Report') {
                applyStructureDataStyle(header, cell);
            }
        });
        
        processedRows++;
        
        // Yield control periodically
        if (i % 100 === 0) {
            await new Promise(resolve => setTimeout(resolve, 0));
        }
    }
    
    self.postMessage({
        type: 'CHUNK_PROCESSED',
        processedRows: processedRows,
        chunkSize: chunk.length
    });
}

async function finalizeWorkbook() {
    worksheet.columns.forEach(column => {
        column.width = 20;
    });
    
    self.postMessage({
        type: 'WORKBOOK_FINALIZED',
        totalRows: processedRows
    });
}

async function generateBuffer() {
    const buffer = await workbook.xlsx.writeBuffer();
    
    self.postMessage({
        type: 'BUFFER_GENERATED',
        buffer: buffer
    }, [buffer]);
}

// Styling helper functions
function applyEmployeeColoring(header, rowData, cell, colorMapping) {
    if (header === 'employee' || header === 'division') {
        const cellColor = colorMapping[rowData.color.toLowerCase()];
        if (cellColor) {
            cell.font = {
                bold: true,
                color: { argb: cellColor },
            };
        }
    }
}

function applyStructureDataStyle(header, cell) {
    if (header === 'brick_name' || header === 'brick_id') {
        cell.font = {
            bold: true,
            color: { argb: '960000' },
        };
    }
}

function applyOverallVisitsStyle(header, rowData, cell) {
    if (header === 'division') {
        cell.font = {
            bold: true,
            color: { argb: '008000' },
        };
    }
    if (header === 'account' || header === 'doctor') {
        cell.font = {
            bold: true,
            color: { argb: '0000ff' },
        };
    }
    if (header === 'acc_type' || header === 'shift') {
        let shiftColor = '000000';
        if (rowData.acc_shift_id === 1) {
            shiftColor = 'EFA609';
        }
        if (rowData.acc_shift_id === 2) {
            shiftColor = 'EF09C2';
        }
        if (rowData.acc_shift_id === 3) {
            shiftColor = '09EFDE';
        }
        cell.font = {
            bold: true,
            color: { argb: shiftColor },
        };
    }
}

function applyEmployeeTrackingStyle(cell) {
    // Add specific styling for employee tracking if needed
    cell.font = { bold: true };
}
