<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Excel Download Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            width: 0%;
            transition: width 0.3s ease;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .memory-info {
            background: #e7f3ff;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>Excel Download Performance Test</h1>
    
    <div class="test-section">
        <h2>Dataset Size Tests</h2>
        <p>Test different dataset sizes to validate performance and memory management:</p>
        
        <button class="test-button" onclick="testSmallDataset()">Small Dataset (1K records)</button>
        <button class="test-button" onclick="testMediumDataset()">Medium Dataset (10K records)</button>
        <button class="test-button" onclick="testLargeDataset()">Large Dataset (50K records)</button>
        <button class="test-button" onclick="testVeryLargeDataset()">Very Large Dataset (100K records)</button>
        <button class="test-button" onclick="testExtremeDataset()" style="background: #dc3545;">Extreme Dataset (135K records)</button>
    </div>

    <div class="test-section">
        <h2>System Compatibility Tests</h2>
        <p>Test browser compatibility and system capabilities:</p>

        <button class="test-button" onclick="runSystemTest()">Run System Test</button>
        <button class="test-button" onclick="testWebWorkerSupport()">Test Web Worker</button>
        <button class="test-button" onclick="testMemoryMonitoring()">Test Memory Monitoring</button>
        
        <div class="progress-bar">
            <div class="progress-fill" id="progressFill"></div>
        </div>
        <div id="progressText">Ready to test</div>
        
        <div class="memory-info" id="memoryInfo">
            Memory usage will be displayed here during tests
        </div>
    </div>
    
    <div class="test-section">
        <h2>Performance Metrics</h2>
        <div id="performanceMetrics">
            <p>No tests run yet</p>
        </div>
    </div>
    
    <div class="test-section">
        <h2>Test Log</h2>
        <div class="log" id="testLog"></div>
        <button class="test-button" onclick="clearLog()">Clear Log</button>
    </div>

    <script>
        // Mock data generation
        function generateTestData(recordCount, columnCount = 10) {
            const columns = [];
            for (let i = 0; i < columnCount; i++) {
                columns.push(`column_${i + 1}`);
            }
            
            const data = [];
            for (let i = 0; i < recordCount; i++) {
                const record = {};
                columns.forEach((col, index) => {
                    if (index === 0) {
                        record[col] = `Record ${i + 1}`;
                    } else if (index === 1) {
                        record[col] = Math.floor(Math.random() * 1000);
                    } else if (index === 2) {
                        record[col] = new Date(2023, Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1).toISOString().split('T')[0];
                    } else {
                        record[col] = `Data ${Math.floor(Math.random() * 100)}`;
                    }
                });
                data.push(record);
            }
            
            return { data, columns };
        }
        
        // Memory monitoring
        function getMemoryUsage() {
            if (performance.memory) {
                return {
                    used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
                    total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024),
                    limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024)
                };
            }
            return null;
        }
        
        function updateMemoryDisplay() {
            const memory = getMemoryUsage();
            const memoryInfo = document.getElementById('memoryInfo');
            
            if (memory) {
                memoryInfo.innerHTML = `
                    <strong>Memory Usage:</strong><br>
                    Used: ${memory.used} MB<br>
                    Total: ${memory.total} MB<br>
                    Limit: ${memory.limit} MB<br>
                    Usage: ${Math.round((memory.used / memory.limit) * 100)}%
                `;
            } else {
                memoryInfo.innerHTML = 'Memory monitoring not available in this browser';
            }
        }
        
        // Logging
        function log(message) {
            const logElement = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }
        
        function clearLog() {
            document.getElementById('testLog').innerHTML = '';
        }
        
        // Progress tracking
        function updateProgress(progress, message) {
            document.getElementById('progressFill').style.width = progress + '%';
            document.getElementById('progressText').textContent = message;
        }
        
        // Test functions
        async function runTest(recordCount, testName) {
            const startTime = performance.now();
            const startMemory = getMemoryUsage();
            
            log(`Starting ${testName} with ${recordCount.toLocaleString()} records`);
            updateProgress(0, `Generating ${recordCount.toLocaleString()} test records...`);
            
            try {
                // Generate test data
                const { data, columns } = generateTestData(recordCount);
                updateProgress(20, 'Test data generated, simulating Excel processing...');
                
                // Simulate chunked processing
                const chunkSize = Math.max(100, Math.min(1000, Math.floor(recordCount / 10)));
                let processedRecords = 0;
                
                for (let i = 0; i < data.length; i += chunkSize) {
                    const chunk = data.slice(i, i + chunkSize);
                    
                    // Simulate processing time
                    await new Promise(resolve => setTimeout(resolve, 10));
                    
                    processedRecords += chunk.length;
                    const progress = 20 + (processedRecords / recordCount) * 70;
                    updateProgress(progress, `Processed ${processedRecords.toLocaleString()} of ${recordCount.toLocaleString()} records`);
                    
                    updateMemoryDisplay();
                    
                    // Check for memory issues
                    const currentMemory = getMemoryUsage();
                    if (currentMemory && currentMemory.used > currentMemory.limit * 0.8) {
                        log(`WARNING: High memory usage detected (${currentMemory.used}MB)`);
                    }
                }
                
                updateProgress(90, 'Simulating file generation...');
                await new Promise(resolve => setTimeout(resolve, 500));
                
                updateProgress(100, 'Test completed successfully!');
                
                const endTime = performance.now();
                const endMemory = getMemoryUsage();
                const duration = Math.round(endTime - startTime);
                
                log(`${testName} completed in ${duration}ms`);
                
                if (startMemory && endMemory) {
                    const memoryDiff = endMemory.used - startMemory.used;
                    log(`Memory usage: ${memoryDiff > 0 ? '+' : ''}${memoryDiff}MB`);
                }
                
                // Update performance metrics
                updatePerformanceMetrics(testName, recordCount, duration, startMemory, endMemory);
                
            } catch (error) {
                log(`ERROR in ${testName}: ${error.message}`);
                updateProgress(0, 'Test failed');
            }
        }
        
        function updatePerformanceMetrics(testName, recordCount, duration, startMemory, endMemory) {
            const metricsElement = document.getElementById('performanceMetrics');
            const recordsPerSecond = Math.round((recordCount / duration) * 1000);
            
            let memoryInfo = '';
            if (startMemory && endMemory) {
                const memoryDiff = endMemory.used - startMemory.used;
                memoryInfo = `<br>Memory Impact: ${memoryDiff > 0 ? '+' : ''}${memoryDiff}MB`;
            }
            
            metricsElement.innerHTML += `
                <div style="border-bottom: 1px solid #eee; padding: 10px 0;">
                    <strong>${testName}</strong><br>
                    Records: ${recordCount.toLocaleString()}<br>
                    Duration: ${duration}ms<br>
                    Performance: ${recordsPerSecond.toLocaleString()} records/second${memoryInfo}
                </div>
            `;
        }
        
        // Test functions
        function testSmallDataset() {
            runTest(1000, 'Small Dataset Test');
        }
        
        function testMediumDataset() {
            runTest(10000, 'Medium Dataset Test');
        }
        
        function testLargeDataset() {
            runTest(50000, 'Large Dataset Test');
        }
        
        function testVeryLargeDataset() {
            if (confirm('This test will generate 100K records and may impact browser performance. Continue?')) {
                runTest(100000, 'Very Large Dataset Test');
            }
        }

        function testExtremeDataset() {
            if (confirm('This test will generate 135K records (similar to your failing case) and may significantly impact browser performance. Continue?')) {
                runTest(135553, 'Extreme Dataset Test (135K records)');
            }
        }

        // Test Web Worker functionality
        function testWebWorkerSupport() {
            console.group('🔧 Web Worker Support Test');

            if (typeof Worker === 'undefined') {
                console.error('❌ Web Workers not supported in this browser');
                return false;
            }

            console.log('✅ Web Workers supported');

            // Test worker loading
            const workerPaths = [
                '/workers/ExcelProcessingWorker.js',
                './workers/ExcelProcessingWorker.js',
                '../workers/ExcelProcessingWorker.js',
                '/public/workers/ExcelProcessingWorker.js'
            ];

            let workerLoaded = false;
            for (const path of workerPaths) {
                try {
                    const testWorker = new Worker(path);
                    console.log(`✅ Worker loaded successfully from: ${path}`);
                    testWorker.terminate();
                    workerLoaded = true;
                    break;
                } catch (error) {
                    console.warn(`❌ Failed to load worker from ${path}:`, error.message);
                }
            }

            if (!workerLoaded) {
                console.error('❌ Could not load Web Worker from any path');
            }

            console.groupEnd();
            return workerLoaded;
        }

        // Test memory monitoring
        function testMemoryMonitoring() {
            console.group('📊 Memory Monitoring Test');

            const memory = getMemoryUsage();
            if (memory) {
                console.log('✅ Memory monitoring available');
                console.log(`Current usage: ${memory.used}MB / ${memory.limit}MB (${Math.round((memory.used / memory.limit) * 100)}%)`);

                if (memory.used / memory.limit > 0.8) {
                    console.warn('⚠️ High memory usage detected');
                }
            } else {
                console.warn('❌ Memory monitoring not available in this browser');
            }

            console.groupEnd();
            return !!memory;
        }

        // Comprehensive system test
        function runSystemTest() {
            console.group('🧪 Comprehensive System Test');

            const results = {
                webWorkerSupport: testWebWorkerSupport(),
                memoryMonitoring: testMemoryMonitoring(),
                browserInfo: {
                    userAgent: navigator.userAgent,
                    language: navigator.language,
                    cookieEnabled: navigator.cookieEnabled,
                    onLine: navigator.onLine
                }
            };

            console.log('System Test Results:', results);

            // Update UI with results
            const metricsElement = document.getElementById('performanceMetrics');
            metricsElement.innerHTML = `
                <div style="border: 2px solid #007bff; padding: 15px; border-radius: 8px; margin-bottom: 10px;">
                    <h3>System Compatibility Test</h3>
                    <p><strong>Web Worker Support:</strong> ${results.webWorkerSupport ? '✅ Available' : '❌ Not Available'}</p>
                    <p><strong>Memory Monitoring:</strong> ${results.memoryMonitoring ? '✅ Available' : '❌ Not Available'}</p>
                    <p><strong>Browser:</strong> ${results.browserInfo.userAgent}</p>
                    <p><strong>Recommended for large datasets:</strong> ${results.webWorkerSupport && results.memoryMonitoring ? '✅ Yes' : '⚠️ Limited support'}</p>
                </div>
            ` + metricsElement.innerHTML;

            console.groupEnd();
            return results;
        }
        
        // Initialize
        updateMemoryDisplay();
        setInterval(updateMemoryDisplay, 2000); // Update memory display every 2 seconds
        
        log('Excel Download Test Suite initialized');
        log('Browser: ' + navigator.userAgent);
        log('Memory monitoring: ' + (performance.memory ? 'Available' : 'Not available'));
    </script>
</body>
</html>
