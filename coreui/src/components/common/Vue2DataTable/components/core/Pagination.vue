<template>
  <div class="pagination-container-vue2" :class="containerClasses">
    <div class="pagination-wrapper">
      <!-- Page Size Selector -->
      <div class="page-size-section">
        <label class="page-size-label">Show:</label>
        <select
          v-model="internalPageSize"
          class="page-size-select"
          :disabled="loading"
          @change="handlePageSizeChange"
        >
          <option
            v-for="size in pageSizeOptions"
            :key="size"
            :value="size"
          >
            {{ size }}
          </option>
        </select>
        <span class="page-size-text">per page</span>
      </div>

      <!-- Page Info -->
      <div class="page-info-section">
        <span class="page-info-text">
          Showing {{ startItem }} to {{ endItem }} of {{ totalItems }} entries
        </span>
      </div>

      <!-- Navigation Controls -->
      <div class="pagination-navigation">
        <!-- First Page -->
        <button
          type="button"
          class="nav-btn nav-btn--first"
          :disabled="isFirstPage || loading"
          @click="goToPage(1)"
          title="First Page"
        >
          <CIcon name="cil-media-skip-backward" />
        </button>

        <!-- Previous Page -->
        <button
          type="button"
          class="nav-btn nav-btn--prev"
          :disabled="isFirstPage || loading"
          @click="goToPage(currentPage - 1)"
          title="Previous Page"
        >
          <CIcon name="cil-chevron-left" />
        </button>

        <!-- Page Numbers -->
        <div class="page-numbers">
          <button
            v-for="page in visiblePages"
            :key="page"
            type="button"
            class="page-btn"
            :class="{
              'page-btn--active': page === currentPage,
              'page-btn--ellipsis': page === '...'
            }"
            :disabled="page === '...' || loading"
            @click="goToPage(page)"
          >
            {{ page }}
          </button>
        </div>

        <!-- Next Page -->
        <button
          type="button"
          class="nav-btn nav-btn--next"
          :disabled="isLastPage || loading"
          @click="goToPage(currentPage + 1)"
          title="Next Page"
        >
          <CIcon name="cil-chevron-right" />
        </button>

        <!-- Last Page -->
        <button
          type="button"
          class="nav-btn nav-btn--last"
          :disabled="isLastPage || loading"
          @click="goToPage(totalPages)"
          title="Last Page"
        >
          <CIcon name="cil-media-skip-forward" />
        </button>
      </div>

      <!-- Jump to Page -->
      <div v-if="showJumpToPage" class="jump-to-page">
        <label class="jump-label">Go to:</label>
        <input
          v-model.number="jumpToPageValue"
          type="number"
          class="jump-input"
          :min="1"
          :max="totalPages"
          :disabled="loading"
          @keyup.enter="handleJumpToPage"
          @blur="handleJumpToPage"
        />
      </div>
    </div>

    <!-- Loading Indicator -->
    <div v-if="loading" class="pagination-loading">
      <div class="loading-spinner-small"></div>
      <span>Loading...</span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Pagination',
  
  props: {
    currentPage: {
      type: Number,
      required: true
    },
    pageSize: {
      type: Number,
      required: true
    },
    totalItems: {
      type: Number,
      required: true
    },
    pageSizeOptions: {
      type: Array,
      default: () => [10, 25, 50, 100, 200]
    },
    loading: {
      type: Boolean,
      default: false
    },
    showJumpToPage: {
      type: Boolean,
      default: true
    },
    maxVisiblePages: {
      type: Number,
      default: 7
    },
    compact: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      internalPageSize: this.pageSize,
      jumpToPageValue: this.currentPage
    }
  },

  computed: {
    /**
     * Container CSS classes
     */
    containerClasses() {
      return [
        'pagination-vue2',
        {
          'pagination--loading': this.loading,
          'pagination--compact': this.compact
        }
      ]
    },

    /**
     * Calculate total pages
     */
    totalPages() {
      return Math.ceil(this.totalItems / this.pageSize)
    },

    /**
     * Check if current page is first
     */
    isFirstPage() {
      return this.currentPage <= 1
    },

    /**
     * Check if current page is last
     */
    isLastPage() {
      return this.currentPage >= this.totalPages
    },

    /**
     * Calculate start item number
     */
    startItem() {
      if (this.totalItems === 0) return 0
      return (this.currentPage - 1) * this.pageSize + 1
    },

    /**
     * Calculate end item number
     */
    endItem() {
      const end = this.currentPage * this.pageSize
      return Math.min(end, this.totalItems)
    },

    /**
     * Get visible page numbers
     */
    visiblePages() {
      const pages = []
      const total = this.totalPages
      const current = this.currentPage
      const max = this.maxVisiblePages

      if (total <= max) {
        // Show all pages if total is less than max
        for (let i = 1; i <= total; i++) {
          pages.push(i)
        }
      } else {
        // Calculate range around current page
        const half = Math.floor(max / 2)
        let start = Math.max(1, current - half)
        let end = Math.min(total, current + half)

        // Adjust if we're near the beginning or end
        if (current <= half) {
          end = max
        } else if (current >= total - half) {
          start = total - max + 1
        }

        // Add first page and ellipsis if needed
        if (start > 1) {
          pages.push(1)
          if (start > 2) {
            pages.push('...')
          }
        }

        // Add visible pages
        for (let i = start; i <= end; i++) {
          pages.push(i)
        }

        // Add ellipsis and last page if needed
        if (end < total) {
          if (end < total - 1) {
            pages.push('...')
          }
          pages.push(total)
        }
      }

      return pages
    }
  },

  methods: {
    /**
     * Go to specific page
     */
    goToPage(page) {
      if (page === '...' || page < 1 || page > this.totalPages || page === this.currentPage) {
        return
      }
      
      this.$emit('page-change', page)
    },

    /**
     * Handle page size change
     */
    handlePageSizeChange() {
      this.$emit('page-size-change', this.internalPageSize)
    },

    /**
     * Handle jump to page
     */
    handleJumpToPage() {
      const page = parseInt(this.jumpToPageValue)
      if (page >= 1 && page <= this.totalPages && page !== this.currentPage) {
        this.goToPage(page)
      } else {
        // Reset to current page if invalid
        this.jumpToPageValue = this.currentPage
      }
    }
  },

  watch: {
    pageSize(newVal) {
      this.internalPageSize = newVal
    },

    currentPage(newVal) {
      this.jumpToPageValue = newVal
    }
  }
}
</script>

<style lang="scss" scoped>
/* Pagination Styles - Matching GenericDataTable Design */

.pagination-container-vue2 {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 0 0 16px 16px;
  padding: 20px 24px;
  border-top: 1px solid #e2e8f0;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);
  position: relative;
}

.pagination-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 16px;
}

/* Page Size Section */
.page-size-section {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #374151;
}

.page-size-label,
.page-size-text {
  font-weight: 500;
  white-space: nowrap;
}

.page-size-select {
  padding: 6px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:focus {
    outline: none;
    border-color: #6366f1;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
  }
  
  &:disabled {
    background: #f9fafb;
    color: #9ca3af;
    cursor: not-allowed;
  }
}

/* Page Info Section */
.page-info-section {
  flex: 1;
  text-align: center;
  min-width: 200px;
}

.page-info-text {
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
  white-space: nowrap;
}

/* Navigation Controls */
.pagination-navigation {
  display: flex;
  align-items: center;
  gap: 4px;
}

.nav-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: 1px solid #d1d5db;
  background: white;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #6b7280;
  
  &:hover:not(:disabled) {
    background: #f3f4f6;
    border-color: #9ca3af;
    color: #374151;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  &:active:not(:disabled) {
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }
  
  &:disabled {
    background: #f9fafb;
    color: #d1d5db;
    cursor: not-allowed;
    border-color: #e5e7eb;
  }
  
  svg {
    width: 16px;
    height: 16px;
  }
}

.page-numbers {
  display: flex;
  align-items: center;
  gap: 2px;
  margin: 0 8px;
}

.page-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 40px;
  height: 40px;
  padding: 0 8px;
  border: 1px solid #d1d5db;
  background: white;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
  
  &:hover:not(:disabled):not(.page-btn--active) {
    background: #f3f4f6;
    border-color: #9ca3af;
    color: #374151;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  &--active {
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    border-color: #6366f1;
    color: white;
    font-weight: 600;
    box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3);
  }
  
  &--ellipsis {
    border: none;
    background: none;
    cursor: default;
    color: #9ca3af;
    
    &:hover {
      background: none;
      transform: none;
      box-shadow: none;
    }
  }
  
  &:disabled {
    background: #f9fafb;
    color: #d1d5db;
    cursor: not-allowed;
    border-color: #e5e7eb;
  }
}

/* Jump to Page */
.jump-to-page {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #374151;
}

.jump-label {
  font-weight: 500;
  white-space: nowrap;
}

.jump-input {
  width: 60px;
  padding: 6px 8px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  font-size: 14px;
  text-align: center;
  transition: all 0.2s ease;
  
  &:focus {
    outline: none;
    border-color: #6366f1;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
  }
  
  &:disabled {
    background: #f9fafb;
    color: #9ca3af;
    cursor: not-allowed;
  }
}

/* Loading Indicator */
.pagination-loading {
  position: absolute;
  top: 50%;
  right: 24px;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #6b7280;
}

.loading-spinner-small {
  width: 16px;
  height: 16px;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #6366f1;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Compact Mode */
.pagination--compact {
  padding: 16px 20px;
  
  .pagination-wrapper {
    gap: 12px;
  }
  
  .nav-btn,
  .page-btn {
    width: 32px;
    height: 32px;
    min-width: 32px;
  }
  
  .page-size-select,
  .jump-input {
    padding: 4px 8px;
    font-size: 13px;
  }
  
  .page-info-text,
  .page-size-label,
  .page-size-text,
  .jump-label {
    font-size: 13px;
  }
}

/* Dark Theme */
.vue2-datatable--dark {
  .pagination-container-vue2 {
    background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
    border-color: #2d3748;
  }
  
  .page-size-select,
  .jump-input,
  .nav-btn,
  .page-btn {
    background: #2d3748;
    border-color: #4a5568;
    color: white;
    
    &:hover:not(:disabled) {
      background: #4a5568;
      border-color: #718096;
    }
    
    &:disabled {
      background: #1a202c;
      color: #4a5568;
    }
  }
  
  .page-btn--active {
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    border-color: #6366f1;
  }
  
  .page-info-text,
  .page-size-label,
  .page-size-text,
  .jump-label {
    color: #a0aec0;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .pagination-container-vue2 {
    padding: 16px 20px;
    border-radius: 0 0 8px 8px;
  }
  
  .pagination-wrapper {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .page-info-section {
    order: -1;
    text-align: center;
  }
  
  .pagination-navigation {
    justify-content: center;
  }
  
  .page-size-section,
  .jump-to-page {
    justify-content: center;
  }
}

@media (max-width: 576px) {
  .page-numbers {
    gap: 1px;
    margin: 0 4px;
  }
  
  .nav-btn,
  .page-btn {
    width: 32px;
    height: 32px;
    min-width: 32px;
    font-size: 12px;
  }
  
  .page-size-section {
    flex-direction: column;
    align-items: center;
    gap: 8px;
  }
  
  .jump-to-page {
    flex-direction: column;
    align-items: center;
    gap: 8px;
  }
}

/* Print Styles */
@media print {
  .pagination-container-vue2 {
    display: none;
  }
}
</style>
