<template>
  <tr
    :class="rowClasses"
    @click="handleClick"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
  >
    <td
      v-for="column in columns"
      :key="column.key"
      :class="getCellClass(column)"
      :style="getCellStyle(column)"
    >
      <!-- Selection Cell -->
      <div v-if="column.key === 'select'" class="cell-select">
        <input
          type="checkbox"
          class="row-checkbox"
          :checked="selected"
          @change="handleSelect"
          @click.stop
          title="Select Row"
        />
      </div>

      <!-- Custom Slot Content -->
      <template v-else-if="$scopedSlots[column.key]">
        <slot
          :name="column.key"
          :item="item"
          :value="getCellValue(column)"
          :column="column"
          :index="index"
        />
      </template>

      <!-- Formatted Content -->
      <div v-else class="cell-content">
        <span
          v-if="column.format"
          v-html="getFormattedValue(column)"
        ></span>
        <span v-else>{{ getCellValue(column) }}</span>
      </div>
    </td>
  </tr>
</template>

<script>
import { applyColumnFormatter, getNestedValue } from '../../utils/formatters.js'

export default {
  name: 'TableRow',
  
  props: {
    item: {
      type: Object,
      required: true
    },
    index: {
      type: Number,
      required: true
    },
    columns: {
      type: Array,
      required: true
    },
    selected: {
      type: Boolean,
      default: false
    },
    selectable: {
      type: Boolean,
      default: false
    },
    selectOnRowClick: {
      type: Boolean,
      default: true
    },
    multipleSelection: {
      type: Boolean,
      default: true
    },
    rowClass: {
      type: [String, Array, Object],
      default: null
    },
    clickable: {
      type: Boolean,
      default: true
    },
    hoverable: {
      type: Boolean,
      default: true
    }
  },

  data() {
    return {
      isHovered: false
    }
  },

  computed: {
    /**
     * Get row CSS classes
     */
    rowClasses() {
      const classes = ['table-row']
      
      // Add custom row classes
      if (this.rowClass) {
        if (Array.isArray(this.rowClass)) {
          classes.push(...this.rowClass)
        } else if (typeof this.rowClass === 'object') {
          Object.entries(this.rowClass).forEach(([className, condition]) => {
            if (condition) classes.push(className)
          })
        } else {
          classes.push(this.rowClass)
        }
      }
      
      // State classes
      if (this.selected) classes.push('table-row--selected')
      if (this.isHovered && this.hoverable) classes.push('table-row--hovered')
      if (this.clickable) classes.push('table-row--clickable')
      if (this.selectable) classes.push('table-row--selectable')
      if (this.selectOnRowClick && this.selectable) classes.push('table-row--select-on-click')
      
      return classes
    }
  },

  methods: {
    /**
     * Get cell CSS class
     */
    getCellClass(column) {
      const classes = ['table-cell', `table-cell--${column.key}`]
      
      // Column-specific classes
      if (column.key === 'select') {
        classes.push('table-cell--select')
      }
      
      if (column.align) {
        classes.push(`table-cell--${column.align}`)
      }
      
      if (column.type) {
        classes.push(`table-cell--${column.type}`)
      }
      
      if (column.fixed) {
        classes.push(`table-cell--fixed-${column.fixed}`)
      }
      
      return classes
    },

    /**
     * Get cell inline styles
     */
    getCellStyle(column) {
      const styles = {}
      
      if (column.width) {
        if (typeof column.width === 'number') {
          styles.width = `${column.width}px`
          styles.minWidth = `${column.width}px`
        } else {
          styles.width = column.width
          styles.minWidth = column.width
        }
      }
      
      if (column.align) {
        styles.textAlign = column.align
      }
      
      return styles
    },

    /**
     * Get cell value
     */
    getCellValue(column) {
      if (column.key === 'select') return null
      
      // Use custom getter if provided
      if (column.getter && typeof column.getter === 'function') {
        return column.getter(this.item, column, this.index)
      }
      
      // Get nested value
      return getNestedValue(this.item, column.key)
    },

    /**
     * Get formatted cell value
     */
    getFormattedValue(column) {
      const value = this.getCellValue(column)
      
      // Apply column formatter
      if (column.format) {
        return applyColumnFormatter(value, column, this.item)
      }
      
      // Apply custom formatter function
      if (column.formatter && typeof column.formatter === 'function') {
        return column.formatter(value, this.item, column, this.index)
      }
      
      return value
    },

    /**
     * Handle row click
     */
    handleClick(event) {
      if (!this.clickable) return

      this.$emit('click', {
        item: this.item,
        index: this.index,
        event,
        selected: this.selected
      })
    },

    /**
     * Handle row selection
     */
    handleSelect(event) {
      this.$emit('select', {
        item: this.item,
        index: this.index,
        selected: event.target.checked,
        event
      })
    },

    /**
     * Handle mouse enter
     */
    handleMouseEnter(event) {
      if (!this.hoverable) return
      
      this.isHovered = true
      this.$emit('hover', {
        item: this.item,
        index: this.index,
        event,
        type: 'enter'
      })
    },

    /**
     * Handle mouse leave
     */
    handleMouseLeave(event) {
      if (!this.hoverable) return
      
      this.isHovered = false
      this.$emit('hover', {
        item: this.item,
        index: this.index,
        event,
        type: 'leave'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
/* Table Row Styles - Matching GenericDataTable Design */

.table-row {
  transition: background-color 0.2s ease, transform 0.1s ease;
  will-change: background-color;
  contain: layout style;
  border-bottom: 1px solid #e2e8f0;
  
  &--clickable {
    cursor: pointer;
  }
  
  &--selectable {
    .table-cell--select {
      opacity: 1;
    }
  }
  
  &--selected {
    background: linear-gradient(135deg, #e6fffa 0%, #d1fae5 100%) !important;
    border-left: 4px solid #10b981;
    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.15);
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(90deg, rgba(16, 185, 129, 0.05) 0%, transparent 100%);
      pointer-events: none;
    }

    .table-cell {
      background-color: inherit;
      color: #065f46;
      font-weight: 500;
    }

    .row-checkbox {
      accent-color: #10b981;
      transform: scale(1.1);
    }
  }

  &--hovered {
    background-color: #f0f9ff;
    transform: translateZ(0);
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);

    .table-cell {
      background-color: inherit;
    }

    &.table-row--selected {
      background: linear-gradient(135deg, #dcfdf7 0%, #bbf7d0 100%) !important;
      box-shadow: 0 4px 12px rgba(16, 185, 129, 0.2);
    }
  }

  &--select-on-click {
    cursor: pointer;
    user-select: none;

    &:active {
      transform: translateY(1px);
    }
  }
  
  &:nth-child(even) {
    background-color: #f8fafc;
  }
}

.table-cell {
  padding: 12px 16px;
  vertical-align: middle;
  border-right: 1px solid #f1f5f9;
  background: inherit;
  will-change: contents;
  contain: layout style;
  
  &:last-child {
    border-right: none;
  }
  
  &--select {
    width: 60px;
    min-width: 60px;
    max-width: 60px;
    text-align: center;
    padding: 12px;
  }
  
  &--left {
    text-align: left;
  }
  
  &--center {
    text-align: center;
  }
  
  &--right {
    text-align: right;
  }
  
  &--number {
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Fira Code', monospace;
    text-align: right;
  }
  
  &--date {
    white-space: nowrap;
  }
  
  &--boolean {
    text-align: center;
  }
  
  &--fixed-left {
    position: sticky;
    left: 0;
    z-index: 10;
    box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);
    background: white;
  }
  
  &--fixed-right {
    position: sticky;
    right: 0;
    z-index: 10;
    box-shadow: -2px 0 4px rgba(0, 0, 0, 0.1);
    background: white;
  }
}

.cell-select {
  display: flex;
  align-items: center;
  justify-content: center;
}

.row-checkbox {
  width: 16px;
  height: 16px;
  cursor: pointer;
  accent-color: #10b981;
  transform: scale(1.1);
  transition: all 0.2s ease;
  border-radius: 3px;

  &:hover {
    transform: scale(1.2);
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.2);
  }

  &:checked {
    background-color: #10b981;
    border-color: #10b981;
  }
}

.cell-content {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  
  span {
    display: inline-block;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

/* Search Highlighting */
:deep(.search-highlight) {
  background: #fef3c7;
  color: #92400e;
  padding: 1px 2px;
  border-radius: 2px;
  font-weight: 600;
}

/* Status Badges */
:deep(.status-active) {
  background: #d1fae5;
  color: #065f46;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

:deep(.status-inactive) {
  background: #fee2e2;
  color: #991b1b;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

:deep(.status-pending) {
  background: #fef3c7;
  color: #92400e;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

:deep(.status-completed) {
  background: #dbeafe;
  color: #1e40af;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

:deep(.status-cancelled) {
  background: #f3f4f6;
  color: #374151;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

/* Dark Theme */
.vue2-datatable--dark {
  .table-row {
    border-color: #2d3748;
    
    &:nth-child(even) {
      background-color: #2d3748;
    }
    
    &--selected {
      background: linear-gradient(135deg, #065f46 0%, #047857 100%) !important;
      border-left-color: #10b981;
      box-shadow: 0 2px 8px rgba(16, 185, 129, 0.25);

      .table-cell {
        color: #d1fae5;
        font-weight: 500;
      }

      .row-checkbox {
        accent-color: #10b981;
      }
    }

    &--hovered {
      background-color: #2d3748;
      box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);

      &.table-row--selected {
        background: linear-gradient(135deg, #047857 0%, #059669 100%) !important;
        box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
      }
    }
  }
  
  .table-cell {
    border-color: #2d3748;
    color: white;
    
    &--fixed-left,
    &--fixed-right {
      background: #1a202c;
    }
  }
  
  :deep(.search-highlight) {
    background: #92400e;
    color: #fef3c7;
  }
}

/* Compact Theme */
.vue2-datatable--compact {
  .table-cell {
    padding: 8px 12px;
    font-size: 13px;
  }
  
  .row-checkbox {
    width: 14px;
    height: 14px;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .table-cell {
    padding: 10px 12px;
    font-size: 14px;
  }
  
  .cell-content {
    max-width: 150px;
  }
  
  .row-checkbox {
    width: 14px;
    height: 14px;
  }
}

@media (max-width: 576px) {
  .table-cell {
    padding: 8px 10px;
    font-size: 13px;
  }
  
  .cell-content {
    max-width: 100px;
  }
  
  .row-checkbox {
    width: 12px;
    height: 12px;
  }
}

/* Print Styles */
@media print {
  .table-row {
    &--hovered {
      background-color: transparent !important;
    }
  }
  
  .row-checkbox {
    display: none;
  }
  
  .cell-select {
    display: none;
  }
  
  .table-cell--select {
    display: none;
  }
}

/* Performance Optimizations */
.table-row {
  contain: layout style;
}

.table-cell {
  contain: layout style;
}
</style>
