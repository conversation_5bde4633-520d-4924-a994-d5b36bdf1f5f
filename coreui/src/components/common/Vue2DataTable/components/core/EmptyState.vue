<template>
  <div class="empty-state-container" :class="containerClasses">
    <div class="empty-state-content">
      <!-- Icon -->
      <div class="empty-state-icon">
        <CIcon :name="iconName" />
      </div>

      <!-- Title -->
      <h3 class="empty-state-title">
        {{ title }}
      </h3>

      <!-- Description -->
      <p class="empty-state-description">
        {{ description }}
      </p>

      <!-- Actions -->
      <div v-if="showActions" class="empty-state-actions">
        <!-- Clear Search -->
        <button
          v-if="hasSearch"
          type="button"
          class="action-button action-button--primary"
          @click="handleClearSearch"
        >
          <CIcon name="cil-x-circle" />
          Clear Search
        </button>

        <!-- Clear Filters -->
        <button
          v-if="hasFilters"
          type="button"
          class="action-button action-button--secondary"
          @click="handleClearFilters"
        >
          <CIcon name="cil-filter-x" />
          Clear Filters
        </button>

        <!-- Refresh -->
        <button
          v-if="showRefresh"
          type="button"
          class="action-button action-button--secondary"
          @click="handleRefresh"
        >
          <CIcon name="cil-reload" />
          Refresh
        </button>

        <!-- Custom Action -->
        <button
          v-if="customAction"
          type="button"
          class="action-button action-button--primary"
          @click="handleCustomAction"
        >
          <CIcon v-if="customAction.icon" :name="customAction.icon" />
          {{ customAction.label }}
        </button>
      </div>

      <!-- Additional Content Slot -->
      <div v-if="$slots.default" class="empty-state-slot">
        <slot />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'EmptyState',
  
  props: {
    hasSearch: {
      type: Boolean,
      default: false
    },
    hasFilters: {
      type: Boolean,
      default: false
    },
    searchTerm: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: null
    },
    description: {
      type: String,
      default: null
    },
    icon: {
      type: String,
      default: null
    },
    showRefresh: {
      type: Boolean,
      default: true
    },
    showActions: {
      type: Boolean,
      default: true
    },
    customAction: {
      type: Object,
      default: null,
      validator: (value) => {
        if (!value) return true
        return value.label && typeof value.label === 'string'
      }
    },
    variant: {
      type: String,
      default: 'default',
      validator: value => ['default', 'search', 'error', 'loading'].includes(value)
    }
  },

  computed: {
    /**
     * Container CSS classes
     */
    containerClasses() {
      return [
        'empty-state-vue2',
        `empty-state--${this.variant}`,
        {
          'empty-state--with-search': this.hasSearch,
          'empty-state--with-filters': this.hasFilters
        }
      ]
    },

    /**
     * Get appropriate icon name
     */
    iconName() {
      if (this.icon) return this.icon
      
      if (this.hasSearch || this.hasFilters) {
        return 'cil-search'
      }
      
      switch (this.variant) {
        case 'search':
          return 'cil-search'
        case 'error':
          return 'cil-warning'
        case 'loading':
          return 'cil-clock'
        default:
          return 'cil-inbox'
      }
    },

    /**
     * Get appropriate title
     */
    computedTitle() {
      if (this.title) return this.title
      
      if (this.hasSearch && this.searchTerm) {
        return `No results for "${this.searchTerm}"`
      }
      
      if (this.hasSearch || this.hasFilters) {
        return 'No matching records found'
      }
      
      switch (this.variant) {
        case 'search':
          return 'No search results'
        case 'error':
          return 'Something went wrong'
        case 'loading':
          return 'Loading data...'
        default:
          return 'No data available'
      }
    },

    /**
     * Get appropriate description
     */
    computedDescription() {
      if (this.description) return this.description
      
      if (this.hasSearch && this.searchTerm) {
        return 'Try adjusting your search criteria or clearing the search to see all records.'
      }
      
      if (this.hasSearch || this.hasFilters) {
        return 'Try adjusting your filters or clearing them to see more results.'
      }
      
      switch (this.variant) {
        case 'search':
          return 'Your search didn\'t return any results. Try different keywords.'
        case 'error':
          return 'We encountered an error while loading the data. Please try again.'
        case 'loading':
          return 'Please wait while we load your data.'
        default:
          return 'There are no records to display at this time.'
      }
    }
  },

  methods: {
    /**
     * Handle clear search action
     */
    handleClearSearch() {
      this.$emit('clear-search')
    },

    /**
     * Handle clear filters action
     */
    handleClearFilters() {
      this.$emit('clear-filters')
    },

    /**
     * Handle refresh action
     */
    handleRefresh() {
      this.$emit('refresh')
    },

    /**
     * Handle custom action
     */
    handleCustomAction() {
      if (this.customAction && this.customAction.handler) {
        this.customAction.handler()
      }
      this.$emit('custom-action', this.customAction)
    }
  }
}
</script>

<style lang="scss" scoped>
/* Empty State Styles */

.empty-state-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  padding: 40px 20px;
  background: #fafbfc;
  border-radius: 8px;
  margin: 20px;
}

.empty-state-vue2 {
  &--search {
    .empty-state-icon {
      color: #6366f1;
    }
  }
  
  &--error {
    .empty-state-icon {
      color: #ef4444;
    }
  }
  
  &--loading {
    .empty-state-icon {
      color: #f59e0b;
    }
  }
}

.empty-state-content {
  text-align: center;
  max-width: 400px;
  width: 100%;
}

.empty-state-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  margin: 0 auto 24px;
  background: #f3f4f6;
  border-radius: 50%;
  color: #9ca3af;
  transition: all 0.3s ease;
  
  svg {
    width: 40px;
    height: 40px;
  }
}

.empty-state-title {
  font-size: 20px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 12px 0;
  line-height: 1.3;
}

.empty-state-description {
  font-size: 14px;
  color: #6b7280;
  line-height: 1.5;
  margin: 0 0 24px 0;
}

.empty-state-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: center;
}

.action-button {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  min-width: 140px;
  justify-content: center;
  
  &--primary {
    background: #6366f1;
    color: white;
    
    &:hover {
      background: #5856eb;
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
    }
    
    &:active {
      transform: translateY(0);
    }
  }
  
  &--secondary {
    background: white;
    color: #374151;
    border: 1px solid #d1d5db;
    
    &:hover {
      background: #f9fafb;
      border-color: #9ca3af;
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
    
    &:active {
      transform: translateY(0);
    }
  }
  
  svg {
    width: 16px;
    height: 16px;
  }
}

.empty-state-slot {
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #e5e7eb;
}

/* Animations */
.empty-state-container {
  animation: fadeIn 0.5s ease;
}

.empty-state-icon {
  animation: bounceIn 0.6s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Dark Theme */
.vue2-datatable--dark {
  .empty-state-container {
    background: #2d3748;
  }
  
  .empty-state-icon {
    background: #4a5568;
    color: #a0aec0;
  }
  
  .empty-state-title {
    color: #e2e8f0;
  }
  
  .empty-state-description {
    color: #a0aec0;
  }
  
  .action-button--secondary {
    background: #4a5568;
    color: white;
    border-color: #718096;
    
    &:hover {
      background: #718096;
      border-color: #a0aec0;
    }
  }
  
  .empty-state-slot {
    border-color: #4a5568;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .empty-state-container {
    min-height: 250px;
    padding: 30px 16px;
    margin: 16px;
  }
  
  .empty-state-icon {
    width: 64px;
    height: 64px;
    margin-bottom: 20px;
    
    svg {
      width: 32px;
      height: 32px;
    }
  }
  
  .empty-state-title {
    font-size: 18px;
    margin-bottom: 10px;
  }
  
  .empty-state-description {
    font-size: 13px;
    margin-bottom: 20px;
  }
  
  .action-button {
    padding: 8px 16px;
    font-size: 13px;
    min-width: 120px;
  }
}

@media (max-width: 576px) {
  .empty-state-container {
    min-height: 200px;
    padding: 24px 12px;
  }
  
  .empty-state-content {
    max-width: 300px;
  }
  
  .empty-state-icon {
    width: 56px;
    height: 56px;
    margin-bottom: 16px;
    
    svg {
      width: 28px;
      height: 28px;
    }
  }
  
  .empty-state-title {
    font-size: 16px;
    margin-bottom: 8px;
  }
  
  .empty-state-description {
    font-size: 12px;
    margin-bottom: 16px;
  }
  
  .action-button {
    width: 100%;
    max-width: 200px;
  }
  
  .empty-state-actions {
    gap: 8px;
  }
}

/* Print Styles */
@media print {
  .empty-state-container {
    background: white;
    box-shadow: none;
  }
  
  .empty-state-actions {
    display: none;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .empty-state-container,
  .empty-state-icon {
    animation: none;
  }
  
  .action-button {
    transition: none;
    
    &:hover {
      transform: none;
    }
  }
}
</style>
