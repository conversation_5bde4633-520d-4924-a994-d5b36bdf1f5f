<template>
  <div class="loading-spinner-container" :class="containerClasses">
    <div class="loading-content">
      <!-- Spinner -->
      <div class="spinner-wrapper">
        <div v-if="type === 'spinner'" class="spinner" :class="spinnerClasses"></div>
        <div v-else-if="type === 'dots'" class="dots-spinner">
          <div class="dot"></div>
          <div class="dot"></div>
          <div class="dot"></div>
        </div>
        <div v-else-if="type === 'pulse'" class="pulse-spinner">
          <div class="pulse-circle"></div>
          <div class="pulse-circle"></div>
          <div class="pulse-circle"></div>
        </div>
        <div v-else-if="type === 'bars'" class="bars-spinner">
          <div class="bar"></div>
          <div class="bar"></div>
          <div class="bar"></div>
          <div class="bar"></div>
          <div class="bar"></div>
        </div>
      </div>

      <!-- Message -->
      <div v-if="message" class="loading-message">
        {{ message }}
      </div>

      <!-- Progress Bar -->
      <div v-if="showProgress && progress !== null" class="progress-container">
        <div class="progress-bar">
          <div 
            class="progress-fill" 
            :style="{ width: `${Math.min(100, Math.max(0, progress))}%` }"
          ></div>
        </div>
        <div class="progress-text">
          {{ Math.round(progress) }}%
        </div>
      </div>

      <!-- Cancel Button -->
      <button
        v-if="cancellable"
        type="button"
        class="cancel-button"
        @click="handleCancel"
      >
        <CIcon name="cil-x" />
        Cancel
      </button>
    </div>

    <!-- Backdrop -->
    <div v-if="overlay" class="loading-backdrop" @click="handleBackdropClick"></div>
  </div>
</template>

<script>
export default {
  name: 'LoadingSpinner',
  
  props: {
    message: {
      type: String,
      default: 'Loading...'
    },
    type: {
      type: String,
      default: 'spinner',
      validator: value => ['spinner', 'dots', 'pulse', 'bars'].includes(value)
    },
    size: {
      type: String,
      default: 'medium',
      validator: value => ['small', 'medium', 'large'].includes(value)
    },
    color: {
      type: String,
      default: 'primary',
      validator: value => ['primary', 'secondary', 'success', 'warning', 'danger'].includes(value)
    },
    overlay: {
      type: Boolean,
      default: true
    },
    cancellable: {
      type: Boolean,
      default: false
    },
    showProgress: {
      type: Boolean,
      default: false
    },
    progress: {
      type: Number,
      default: null,
      validator: value => value === null || (value >= 0 && value <= 100)
    },
    fullscreen: {
      type: Boolean,
      default: false
    }
  },

  computed: {
    /**
     * Container CSS classes
     */
    containerClasses() {
      return [
        'loading-spinner-vue2',
        `loading-spinner--${this.size}`,
        `loading-spinner--${this.color}`,
        {
          'loading-spinner--overlay': this.overlay,
          'loading-spinner--fullscreen': this.fullscreen,
          'loading-spinner--cancellable': this.cancellable
        }
      ]
    },

    /**
     * Spinner CSS classes
     */
    spinnerClasses() {
      return [
        `spinner--${this.size}`,
        `spinner--${this.color}`
      ]
    }
  },

  methods: {
    /**
     * Handle cancel button click
     */
    handleCancel() {
      this.$emit('cancel')
    },

    /**
     * Handle backdrop click
     */
    handleBackdropClick() {
      if (this.cancellable) {
        this.handleCancel()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
/* Loading Spinner Styles */

.loading-spinner-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-spinner-vue2 {
  &--overlay {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(2px);
  }
  
  &--fullscreen {
    position: fixed;
    z-index: 9999;
  }
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  max-width: 300px;
  text-align: center;
}

.spinner-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Spinner Types */

/* Classic Spinner */
.spinner {
  border-radius: 50%;
  animation: spin 1s linear infinite;
  
  &--small {
    width: 24px;
    height: 24px;
    border: 2px solid #e5e7eb;
    border-top: 2px solid currentColor;
  }
  
  &--medium {
    width: 32px;
    height: 32px;
    border: 3px solid #e5e7eb;
    border-top: 3px solid currentColor;
  }
  
  &--large {
    width: 48px;
    height: 48px;
    border: 4px solid #e5e7eb;
    border-top: 4px solid currentColor;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Dots Spinner */
.dots-spinner {
  display: flex;
  gap: 4px;
  
  .dot {
    border-radius: 50%;
    background: currentColor;
    animation: dotPulse 1.4s ease-in-out infinite both;
    
    &:nth-child(1) { animation-delay: -0.32s; }
    &:nth-child(2) { animation-delay: -0.16s; }
    &:nth-child(3) { animation-delay: 0s; }
  }
}

.loading-spinner--small .dots-spinner .dot {
  width: 6px;
  height: 6px;
}

.loading-spinner--medium .dots-spinner .dot {
  width: 8px;
  height: 8px;
}

.loading-spinner--large .dots-spinner .dot {
  width: 12px;
  height: 12px;
}

@keyframes dotPulse {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Pulse Spinner */
.pulse-spinner {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  
  .pulse-circle {
    position: absolute;
    border-radius: 50%;
    border: 2px solid currentColor;
    animation: pulse 2s ease-in-out infinite;
    
    &:nth-child(1) { animation-delay: 0s; }
    &:nth-child(2) { animation-delay: 0.4s; }
    &:nth-child(3) { animation-delay: 0.8s; }
  }
}

.loading-spinner--small .pulse-spinner .pulse-circle {
  width: 20px;
  height: 20px;
}

.loading-spinner--medium .pulse-spinner .pulse-circle {
  width: 30px;
  height: 30px;
}

.loading-spinner--large .pulse-spinner .pulse-circle {
  width: 45px;
  height: 45px;
}

@keyframes pulse {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0;
  }
}

/* Bars Spinner */
.bars-spinner {
  display: flex;
  gap: 2px;
  align-items: end;
  
  .bar {
    background: currentColor;
    animation: barStretch 1.2s ease-in-out infinite;
    
    &:nth-child(1) { animation-delay: -1.2s; }
    &:nth-child(2) { animation-delay: -1.1s; }
    &:nth-child(3) { animation-delay: -1.0s; }
    &:nth-child(4) { animation-delay: -0.9s; }
    &:nth-child(5) { animation-delay: -0.8s; }
  }
}

.loading-spinner--small .bars-spinner .bar {
  width: 3px;
  height: 20px;
}

.loading-spinner--medium .bars-spinner .bar {
  width: 4px;
  height: 30px;
}

.loading-spinner--large .bars-spinner .bar {
  width: 6px;
  height: 45px;
}

@keyframes barStretch {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}

/* Color Variants */
.loading-spinner--primary {
  color: #6366f1;
}

.loading-spinner--secondary {
  color: #6b7280;
}

.loading-spinner--success {
  color: #10b981;
}

.loading-spinner--warning {
  color: #f59e0b;
}

.loading-spinner--danger {
  color: #ef4444;
}

/* Loading Message */
.loading-message {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin: 0;
}

/* Progress Bar */
.progress-container {
  width: 100%;
  max-width: 200px;
}

.progress-bar {
  width: 100%;
  height: 6px;
  background: #e5e7eb;
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: currentColor;
  border-radius: 3px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 12px;
  font-weight: 600;
  color: #6b7280;
  margin-top: 8px;
}

/* Cancel Button */
.cancel-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  color: #374151;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background: #e5e7eb;
    border-color: #9ca3af;
  }
  
  &:active {
    transform: translateY(1px);
  }
  
  svg {
    width: 14px;
    height: 14px;
  }
}

/* Backdrop */
.loading-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
}

/* Dark Theme */
.vue2-datatable--dark {
  .loading-spinner-vue2--overlay {
    background: rgba(26, 32, 44, 0.9);
  }
  
  .loading-content {
    background: #2d3748;
    color: white;
  }
  
  .loading-message {
    color: #e2e8f0;
  }
  
  .progress-bar {
    background: #4a5568;
  }
  
  .progress-text {
    color: #a0aec0;
  }
  
  .cancel-button {
    background: #4a5568;
    border-color: #718096;
    color: white;
    
    &:hover {
      background: #718096;
      border-color: #a0aec0;
    }
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .loading-content {
    padding: 20px;
    max-width: 250px;
    gap: 12px;
  }
  
  .loading-message {
    font-size: 13px;
  }
  
  .cancel-button {
    padding: 6px 12px;
    font-size: 12px;
  }
}

@media (max-width: 576px) {
  .loading-content {
    padding: 16px;
    max-width: 200px;
    gap: 10px;
  }
  
  .loading-message {
    font-size: 12px;
  }
  
  .progress-container {
    max-width: 150px;
  }
}

/* Animation Performance */
.spinner,
.dots-spinner .dot,
.pulse-spinner .pulse-circle,
.bars-spinner .bar {
  will-change: transform, opacity;
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .spinner,
  .dots-spinner .dot,
  .pulse-spinner .pulse-circle,
  .bars-spinner .bar {
    animation-duration: 2s;
  }
}
</style>
