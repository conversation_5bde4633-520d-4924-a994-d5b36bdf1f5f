/**
 * Performance monitoring utilities for Vue2 DataTable
 * Provides performance tracking, memory monitoring, and optimization helpers
 */

/**
 * Performance monitor class for tracking render times and memory usage
 */
export class PerformanceMonitor {
  constructor(options = {}) {
    this.enabled = options.enabled !== false
    this.warningThreshold = options.warningThreshold || 100 // ms
    this.memoryWarningThreshold = options.memoryWarningThreshold || 50 // MB
    this.measurements = new Map()
    this.memorySnapshots = []
    this.maxSnapshots = options.maxSnapshots || 100
  }

  /**
   * Start measuring performance for a specific operation
   * @param {string} name - Operation name
   * @returns {Function} End measurement function
   */
  startMeasurement(name) {
    if (!this.enabled) return () => {}

    const startTime = performance.now()
    const startMemory = this.getMemoryUsage()

    return () => {
      const endTime = performance.now()
      const endMemory = this.getMemoryUsage()
      const duration = endTime - startTime
      const memoryDelta = endMemory - startMemory

      this.recordMeasurement(name, {
        duration,
        startTime,
        endTime,
        startMemory,
        endMemory,
        memoryDelta
      })

      if (duration > this.warningThreshold) {
        console.warn(`Vue2DataTable Performance Warning: ${name} took ${duration.toFixed(2)}ms`)
      }

      return { duration, memoryDelta }
    }
  }

  /**
   * Record a measurement
   * @param {string} name - Operation name
   * @param {Object} measurement - Measurement data
   */
  recordMeasurement(name, measurement) {
    if (!this.measurements.has(name)) {
      this.measurements.set(name, [])
    }

    const measurements = this.measurements.get(name)
    measurements.push(measurement)

    // Keep only last 100 measurements per operation
    if (measurements.length > 100) {
      measurements.shift()
    }
  }

  /**
   * Get memory usage in MB
   * @returns {number} Memory usage in MB
   */
  getMemoryUsage() {
    if (performance.memory) {
      return performance.memory.usedJSHeapSize / 1024 / 1024
    }
    return 0
  }

  /**
   * Take a memory snapshot
   * @param {string} label - Snapshot label
   */
  takeMemorySnapshot(label = 'snapshot') {
    const memory = this.getMemoryUsage()
    const timestamp = Date.now()

    this.memorySnapshots.push({
      label,
      memory,
      timestamp
    })

    // Keep only recent snapshots
    if (this.memorySnapshots.length > this.maxSnapshots) {
      this.memorySnapshots.shift()
    }

    if (memory > this.memoryWarningThreshold) {
      console.warn(`Vue2DataTable Memory Warning: ${memory.toFixed(2)}MB used`)
    }
  }

  /**
   * Get performance statistics for an operation
   * @param {string} name - Operation name
   * @returns {Object} Performance statistics
   */
  getStats(name) {
    const measurements = this.measurements.get(name)
    if (!measurements || measurements.length === 0) {
      return null
    }

    const durations = measurements.map(m => m.duration)
    const memoryDeltas = measurements.map(m => m.memoryDelta)

    return {
      count: measurements.length,
      avgDuration: durations.reduce((a, b) => a + b, 0) / durations.length,
      minDuration: Math.min(...durations),
      maxDuration: Math.max(...durations),
      avgMemoryDelta: memoryDeltas.reduce((a, b) => a + b, 0) / memoryDeltas.length,
      lastMeasurement: measurements[measurements.length - 1]
    }
  }

  /**
   * Get all performance statistics
   * @returns {Object} All performance statistics
   */
  getAllStats() {
    const stats = {}
    for (const [name] of this.measurements) {
      stats[name] = this.getStats(name)
    }
    return stats
  }

  /**
   * Clear all measurements
   */
  clear() {
    this.measurements.clear()
    this.memorySnapshots = []
  }

  /**
   * Generate performance report
   * @returns {string} Performance report
   */
  generateReport() {
    const stats = this.getAllStats()
    const currentMemory = this.getMemoryUsage()
    
    let report = `Vue2DataTable Performance Report\n`
    report += `Current Memory Usage: ${currentMemory.toFixed(2)}MB\n\n`

    for (const [operation, stat] of Object.entries(stats)) {
      if (stat) {
        report += `${operation}:\n`
        report += `  Count: ${stat.count}\n`
        report += `  Avg Duration: ${stat.avgDuration.toFixed(2)}ms\n`
        report += `  Min Duration: ${stat.minDuration.toFixed(2)}ms\n`
        report += `  Max Duration: ${stat.maxDuration.toFixed(2)}ms\n`
        report += `  Avg Memory Delta: ${stat.avgMemoryDelta.toFixed(2)}MB\n\n`
      }
    }

    return report
  }
}

/**
 * Global performance monitor instance
 */
export const globalPerformanceMonitor = new PerformanceMonitor()

/**
 * Debounced function to optimize frequent operations
 * @param {Function} fn - Function to debounce
 * @param {number} delay - Delay in milliseconds
 * @returns {Function} Debounced function
 */
export function debouncePerformance(fn, delay = 16) {
  let timeoutId
  return function (...args) {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => fn.apply(this, args), delay)
  }
}

/**
 * Throttled function for scroll operations
 * @param {Function} fn - Function to throttle
 * @param {number} delay - Delay in milliseconds
 * @returns {Function} Throttled function
 */
export function throttlePerformance(fn, delay = 16) {
  let lastCall = 0
  return function (...args) {
    const now = Date.now()
    if (now - lastCall >= delay) {
      lastCall = now
      return fn.apply(this, args)
    }
  }
}

/**
 * Optimize array operations for large datasets
 * @param {Array} array - Array to process
 * @param {Function} processor - Processing function
 * @param {number} chunkSize - Chunk size for processing
 * @returns {Promise} Promise that resolves when processing is complete
 */
export function processArrayInChunks(array, processor, chunkSize = 1000) {
  return new Promise((resolve) => {
    let index = 0
    
    function processChunk() {
      const chunk = array.slice(index, index + chunkSize)
      if (chunk.length === 0) {
        resolve()
        return
      }

      processor(chunk, index)
      index += chunkSize

      // Use requestAnimationFrame to avoid blocking the UI
      requestAnimationFrame(processChunk)
    }

    processChunk()
  })
}

/**
 * Memory-efficient object freezing for large datasets
 * @param {Object} obj - Object to freeze
 * @param {boolean} deep - Whether to deep freeze
 * @returns {Object} Frozen object
 */
export function freezeObject(obj, deep = false) {
  if (!obj || typeof obj !== 'object') return obj

  if (deep) {
    Object.getOwnPropertyNames(obj).forEach(prop => {
      if (obj[prop] && typeof obj[prop] === 'object') {
        freezeObject(obj[prop], true)
      }
    })
  }

  return Object.freeze(obj)
}

/**
 * Check if browser supports required performance features
 * @returns {Object} Feature support information
 */
export function checkBrowserSupport() {
  return {
    performance: typeof performance !== 'undefined',
    performanceMemory: !!(performance && performance.memory),
    requestAnimationFrame: typeof requestAnimationFrame !== 'undefined',
    intersectionObserver: typeof IntersectionObserver !== 'undefined',
    resizeObserver: typeof ResizeObserver !== 'undefined',
    webWorkers: typeof Worker !== 'undefined'
  }
}

/**
 * Estimate memory usage of an object
 * @param {any} obj - Object to estimate
 * @returns {number} Estimated size in bytes
 */
export function estimateObjectSize(obj) {
  const seen = new WeakSet()
  
  function sizeOf(obj) {
    if (obj === null || obj === undefined) return 0
    if (typeof obj === 'boolean') return 4
    if (typeof obj === 'number') return 8
    if (typeof obj === 'string') return obj.length * 2
    if (typeof obj === 'object') {
      if (seen.has(obj)) return 0
      seen.add(obj)
      
      let size = 0
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          size += sizeOf(key) + sizeOf(obj[key])
        }
      }
      return size
    }
    return 0
  }
  
  return sizeOf(obj)
}

/**
 * Performance-optimized array search
 * @param {Array} array - Array to search
 * @param {string} searchTerm - Search term
 * @param {Array} searchFields - Fields to search in
 * @param {Object} options - Search options
 * @returns {Array} Filtered array
 */
export function performantSearch(array, searchTerm, searchFields, options = {}) {
  if (!searchTerm || searchTerm.length < (options.minLength || 2)) {
    return array
  }

  const { caseSensitive = false, exactMatch = false } = options
  const term = caseSensitive ? searchTerm : searchTerm.toLowerCase()

  return array.filter(item => {
    return searchFields.some(field => {
      const value = getNestedValue(item, field)
      if (value === null || value === undefined) return false
      
      const stringValue = caseSensitive ? String(value) : String(value).toLowerCase()
      
      return exactMatch ? stringValue === term : stringValue.includes(term)
    })
  })
}

/**
 * Get nested value from object path
 * @param {Object} obj - Object to search
 * @param {string} path - Dot notation path
 * @returns {any} Value at path
 */
function getNestedValue(obj, path) {
  return path.split('.').reduce((current, key) => {
    return current && current[key] !== undefined ? current[key] : undefined
  }, obj)
}

/**
 * Create a performance-optimized virtual list calculator with browser freeze prevention
 * @param {Object} options - Configuration options
 * @returns {Object} Virtual list calculator
 */
export function createVirtualListCalculator(options = {}) {
  const {
    itemHeight = 40,
    containerHeight = 400,
    bufferSize = 5,
    overscan = 3,
    maxItemsThreshold = 500000, // Maximum items before fallback
    performanceThreshold = 100 // Max calculation time in ms
  } = options

  return {
    getVisibleRange(scrollTop, itemCount, currentContainerHeight = containerHeight) {
      const startTime = performance.now()

      // ENHANCED: Browser freeze prevention
      if (itemCount > maxItemsThreshold) {
        console.warn(`Vue2DataTable: Large dataset detected (${itemCount} items). Using fallback mode.`)
        return this.getFallbackRange(scrollTop, itemCount, currentContainerHeight)
      }

      if (itemCount === 0) {
        return {
          startIndex: 0,
          endIndex: 0,
          offsetY: 0,
          visibleCount: 0,
          actualStartIndex: 0,
          actualEndIndex: 0,
          performanceWarning: false
        }
      }

      // Ensure we have valid values
      const safeScrollTop = Math.max(0, scrollTop || 0)
      const safeContainerHeight = Math.max(100, currentContainerHeight || containerHeight)
      const safeItemHeight = Math.max(1, itemHeight)
      const safeItemCount = Math.max(0, itemCount)

      // ENHANCED: Performance monitoring during calculation
      const calculationStart = performance.now()

      // Calculate the first visible item index
      const startIndex = Math.floor(safeScrollTop / safeItemHeight)

      // Calculate how many items can fit in the container (add extra for partial items)
      const visibleItemCount = Math.ceil(safeContainerHeight / safeItemHeight) + 1

      // Calculate the last visible item index
      const endIndex = Math.min(
        startIndex + visibleItemCount - 1,
        safeItemCount - 1
      )

      // ENHANCED: Adaptive overscan based on dataset size
      const adaptiveOverscan = itemCount > 10000 ? Math.min(overscan, 2) : overscan
      const adaptiveBuffer = itemCount > 50000 ? Math.min(bufferSize, 3) : bufferSize

      // Apply overscan for smoother scrolling
      const overscanStartIndex = Math.max(0, startIndex - adaptiveOverscan)
      const overscanEndIndex = Math.min(safeItemCount - 1, endIndex + adaptiveOverscan)

      // Apply buffer to start and end indices for performance
      const bufferedStartIndex = Math.max(0, overscanStartIndex - adaptiveBuffer)
      const bufferedEndIndex = Math.min(safeItemCount - 1, overscanEndIndex + adaptiveBuffer)

      // Calculate visible count
      const visibleCount = Math.max(0, bufferedEndIndex - bufferedStartIndex + 1)

      const calculationTime = performance.now() - calculationStart
      const performanceWarning = calculationTime > performanceThreshold

      if (performanceWarning) {
        console.warn(`Vue2DataTable: Slow virtual scroll calculation (${calculationTime.toFixed(2)}ms)`)
      }

      const result = {
        startIndex: bufferedStartIndex,
        endIndex: bufferedEndIndex,
        offsetY: bufferedStartIndex * safeItemHeight,
        visibleCount: visibleCount,
        actualStartIndex: startIndex,
        actualEndIndex: endIndex,
        performanceWarning,
        calculationTime,
        // Debug information
        debug: {
          scrollTop: safeScrollTop,
          containerHeight: safeContainerHeight,
          itemHeight: safeItemHeight,
          itemCount: safeItemCount,
          visibleItemCount: visibleItemCount,
          overscanStartIndex,
          overscanEndIndex,
          adaptiveOverscan,
          adaptiveBuffer
        }
      }

      // Debug logging in development
      if (process.env.NODE_ENV === 'development') {
        console.log('Virtual List Calculator:', result)
      }

      return result
    },

    /**
     * Fallback range calculation for very large datasets
     */
    getFallbackRange(scrollTop, itemCount, currentContainerHeight = containerHeight) {
      const safeScrollTop = Math.max(0, scrollTop || 0)
      const safeContainerHeight = Math.max(100, currentContainerHeight || containerHeight)
      const safeItemHeight = Math.max(1, itemHeight)
      const safeItemCount = Math.max(0, itemCount)

      // Simplified calculation for performance
      const startIndex = Math.floor(safeScrollTop / safeItemHeight)
      const visibleItemCount = Math.ceil(safeContainerHeight / safeItemHeight) + 1
      const endIndex = Math.min(startIndex + visibleItemCount - 1, safeItemCount - 1)

      // Minimal buffer for large datasets
      const minimalBuffer = 2
      const bufferedStartIndex = Math.max(0, startIndex - minimalBuffer)
      const bufferedEndIndex = Math.min(safeItemCount - 1, endIndex + minimalBuffer)

      return {
        startIndex: bufferedStartIndex,
        endIndex: bufferedEndIndex,
        offsetY: bufferedStartIndex * safeItemHeight,
        visibleCount: bufferedEndIndex - bufferedStartIndex + 1,
        actualStartIndex: startIndex,
        actualEndIndex: endIndex,
        performanceWarning: false,
        fallbackMode: true,
        calculationTime: 0
      }
    },

    getTotalHeight(itemCount) {
      return Math.max(0, itemCount) * Math.max(1, itemHeight)
    },

    getItemTop(index) {
      return Math.max(0, index) * Math.max(1, itemHeight)
    },

    getScrollTopForIndex(index) {
      return Math.max(0, index) * Math.max(1, itemHeight)
    },

    isIndexVisible(index, scrollTop, currentContainerHeight = containerHeight) {
      const range = this.getVisibleRange(scrollTop, Number.MAX_SAFE_INTEGER, currentContainerHeight)
      return index >= range.actualStartIndex && index <= range.actualEndIndex
    }
  }
}
