/**
 * Validation utilities for Vue2 DataTable
 * Provides prop validation and data validation functions
 */

/**
 * Validate column configuration
 * @param {Array} columns - Array of column objects
 * @returns {boolean} True if valid
 */
export function validateColumns(columns) {
  if (!Array.isArray(columns)) {
    console.error('Vue2DataTable: columns must be an array')
    return false
  }

  if (columns.length === 0) {
    console.warn('Vue2DataTable: columns array is empty')
    return true
  }

  for (let i = 0; i < columns.length; i++) {
    const column = columns[i]
    
    if (!column || typeof column !== 'object') {
      console.error(`Vue2DataTable: column at index ${i} must be an object`)
      return false
    }

    if (!column.key || typeof column.key !== 'string') {
      console.error(`Vue2DataTable: column at index ${i} must have a valid 'key' property`)
      return false
    }

    if (!column.label || typeof column.label !== 'string') {
      console.error(`Vue2DataTable: column at index ${i} must have a valid 'label' property`)
      return false
    }

    // Validate optional properties
    if (column.sortable !== undefined && typeof column.sortable !== 'boolean') {
      console.error(`Vue2DataTable: column '${column.key}' sortable property must be boolean`)
      return false
    }

    if (column.searchable !== undefined && typeof column.searchable !== 'boolean') {
      console.error(`Vue2DataTable: column '${column.key}' searchable property must be boolean`)
      return false
    }

    if (column.width !== undefined && typeof column.width !== 'string' && typeof column.width !== 'number') {
      console.error(`Vue2DataTable: column '${column.key}' width property must be string or number`)
      return false
    }

    if (column.align !== undefined && !['left', 'center', 'right'].includes(column.align)) {
      console.error(`Vue2DataTable: column '${column.key}' align property must be 'left', 'center', or 'right'`)
      return false
    }

    if (column.fixed !== undefined && !['left', 'right'].includes(column.fixed)) {
      console.error(`Vue2DataTable: column '${column.key}' fixed property must be 'left' or 'right'`)
      return false
    }

    if (column.render !== undefined && typeof column.render !== 'function') {
      console.error(`Vue2DataTable: column '${column.key}' render property must be a function`)
      return false
    }

    if (column.formatter !== undefined && typeof column.formatter !== 'function') {
      console.error(`Vue2DataTable: column '${column.key}' formatter property must be a function`)
      return false
    }

    if (column.validator !== undefined && typeof column.validator !== 'function') {
      console.error(`Vue2DataTable: column '${column.key}' validator property must be a function`)
      return false
    }
  }

  return true
}

/**
 * Validate data source
 * @param {Array|String|Function} dataSource - Data source configuration
 * @returns {boolean} True if valid
 */
export function validateDataSource(dataSource) {
  if (Array.isArray(dataSource)) {
    return true // Array data is always valid
  }

  if (typeof dataSource === 'string') {
    try {
      new URL(dataSource)
      return true
    } catch {
      console.error('Vue2DataTable: dataSource string must be a valid URL')
      return false
    }
  }

  if (typeof dataSource === 'function') {
    return true // Function data source is valid
  }

  console.error('Vue2DataTable: dataSource must be an array, string URL, or function')
  return false
}

/**
 * Validate pagination configuration
 * @param {Object} pagination - Pagination configuration
 * @returns {boolean} True if valid
 */
export function validatePagination(pagination) {
  if (!pagination || typeof pagination !== 'object') {
    return true // Pagination is optional
  }

  if (pagination.pageSize !== undefined) {
    if (!Number.isInteger(pagination.pageSize) || pagination.pageSize < 1) {
      console.error('Vue2DataTable: pagination.pageSize must be a positive integer')
      return false
    }
  }

  if (pagination.currentPage !== undefined) {
    if (!Number.isInteger(pagination.currentPage) || pagination.currentPage < 1) {
      console.error('Vue2DataTable: pagination.currentPage must be a positive integer')
      return false
    }
  }

  if (pagination.total !== undefined) {
    if (!Number.isInteger(pagination.total) || pagination.total < 0) {
      console.error('Vue2DataTable: pagination.total must be a non-negative integer')
      return false
    }
  }

  if (pagination.pageSizeOptions !== undefined) {
    if (!Array.isArray(pagination.pageSizeOptions)) {
      console.error('Vue2DataTable: pagination.pageSizeOptions must be an array')
      return false
    }

    for (const option of pagination.pageSizeOptions) {
      if (!Number.isInteger(option) || option < 1) {
        console.error('Vue2DataTable: pagination.pageSizeOptions must contain positive integers')
        return false
      }
    }
  }

  return true
}

/**
 * Validate virtual scrolling configuration
 * @param {Object} virtualScroll - Virtual scrolling configuration
 * @returns {boolean} True if valid
 */
export function validateVirtualScroll(virtualScroll) {
  if (!virtualScroll || typeof virtualScroll !== 'object') {
    return true // Virtual scrolling is optional
  }

  if (virtualScroll.enabled !== undefined && typeof virtualScroll.enabled !== 'boolean') {
    console.error('Vue2DataTable: virtualScroll.enabled must be boolean')
    return false
  }

  if (virtualScroll.rowHeight !== undefined) {
    if (!Number.isInteger(virtualScroll.rowHeight) || virtualScroll.rowHeight < 1) {
      console.error('Vue2DataTable: virtualScroll.rowHeight must be a positive integer')
      return false
    }
  }

  if (virtualScroll.bufferSize !== undefined) {
    if (!Number.isInteger(virtualScroll.bufferSize) || virtualScroll.bufferSize < 0) {
      console.error('Vue2DataTable: virtualScroll.bufferSize must be a non-negative integer')
      return false
    }
  }

  if (virtualScroll.threshold !== undefined) {
    if (!Number.isInteger(virtualScroll.threshold) || virtualScroll.threshold < 1) {
      console.error('Vue2DataTable: virtualScroll.threshold must be a positive integer')
      return false
    }
  }

  return true
}

/**
 * Validate search configuration
 * @param {Object} search - Search configuration
 * @returns {boolean} True if valid
 */
export function validateSearch(search) {
  if (!search || typeof search !== 'object') {
    return true // Search is optional
  }

  if (search.enabled !== undefined && typeof search.enabled !== 'boolean') {
    console.error('Vue2DataTable: search.enabled must be boolean')
    return false
  }

  if (search.debounceDelay !== undefined) {
    if (!Number.isInteger(search.debounceDelay) || search.debounceDelay < 0) {
      console.error('Vue2DataTable: search.debounceDelay must be a non-negative integer')
      return false
    }
  }

  if (search.minLength !== undefined) {
    if (!Number.isInteger(search.minLength) || search.minLength < 0) {
      console.error('Vue2DataTable: search.minLength must be a non-negative integer')
      return false
    }
  }

  if (search.caseSensitive !== undefined && typeof search.caseSensitive !== 'boolean') {
    console.error('Vue2DataTable: search.caseSensitive must be boolean')
    return false
  }

  if (search.exactMatch !== undefined && typeof search.exactMatch !== 'boolean') {
    console.error('Vue2DataTable: search.exactMatch must be boolean')
    return false
  }

  return true
}

/**
 * Validate selection configuration
 * @param {Object} selection - Selection configuration
 * @returns {boolean} True if valid
 */
export function validateSelection(selection) {
  if (!selection || typeof selection !== 'object') {
    return true // Selection is optional
  }

  if (selection.enabled !== undefined && typeof selection.enabled !== 'boolean') {
    console.error('Vue2DataTable: selection.enabled must be boolean')
    return false
  }

  if (selection.multiple !== undefined && typeof selection.multiple !== 'boolean') {
    console.error('Vue2DataTable: selection.multiple must be boolean')
    return false
  }

  if (selection.selectAll !== undefined && typeof selection.selectAll !== 'boolean') {
    console.error('Vue2DataTable: selection.selectAll must be boolean')
    return false
  }

  return true
}

/**
 * Validate theme configuration
 * @param {string} theme - Theme name
 * @returns {boolean} True if valid
 */
export function validateTheme(theme) {
  const validThemes = ['default', 'dark', 'compact', 'minimal']
  
  if (theme && !validThemes.includes(theme)) {
    console.error(`Vue2DataTable: theme must be one of: ${validThemes.join(', ')}`)
    return false
  }

  return true
}

/**
 * Validate performance configuration
 * @param {Object} performance - Performance configuration
 * @returns {boolean} True if valid
 */
export function validatePerformance(performance) {
  if (!performance || typeof performance !== 'object') {
    return true // Performance config is optional
  }

  if (performance.monitoring !== undefined && typeof performance.monitoring !== 'boolean') {
    console.error('Vue2DataTable: performance.monitoring must be boolean')
    return false
  }

  if (performance.maxRows !== undefined) {
    if (!Number.isInteger(performance.maxRows) || performance.maxRows < 1) {
      console.error('Vue2DataTable: performance.maxRows must be a positive integer')
      return false
    }
  }

  if (performance.warningThreshold !== undefined) {
    if (!Number.isInteger(performance.warningThreshold) || performance.warningThreshold < 1) {
      console.error('Vue2DataTable: performance.warningThreshold must be a positive integer')
      return false
    }
  }

  return true
}

/**
 * Comprehensive prop validation for Vue2DataTable
 * @param {Object} props - Component props
 * @returns {boolean} True if all props are valid
 */
export function validateProps(props) {
  const validations = [
    () => validateColumns(props.columns),
    () => validateDataSource(props.dataSource),
    () => validatePagination(props.pagination),
    () => validateVirtualScroll(props.virtualScroll),
    () => validateSearch(props.search),
    () => validateSelection(props.selection),
    () => validateTheme(props.theme),
    () => validatePerformance(props.performance)
  ]

  return validations.every(validation => validation())
}
