# Vue2 CoreUI High-Performance DataTable

A highly optimized, feature-rich data table component for Vue 2 applications with CoreUI styling. Designed to handle massive datasets (500K+ records, 60+ columns) with virtual scrolling, advanced search, and comprehensive customization options.

## 🚀 Features

### Core Features
- ✅ **Vue 2 Compatible** - Full integration with Vue 2.7+ ecosystem
- ✅ **Virtual Scrolling** - Handle 500,000+ records with smooth performance
- ✅ **Column Virtualization** - Support for 60+ columns efficiently
- ✅ **Sticky Headers** - Headers remain visible during scrolling
- ✅ **Advanced Search** - Debounced search with performance optimization
- ✅ **Multi-Column Sorting** - Sort by multiple columns with visual indicators
- ✅ **Row Selection** - Single and multi-row selection with "select all"
- ✅ **Pagination** - Attractive pagination with customizable page sizes
- ✅ **Real-time Updates** - Reactive to data changes
- ✅ **Responsive Design** - Works on all screen sizes

### Performance Features
- ✅ **Performance Monitoring** - Real-time performance metrics
- ✅ **Memory Management** - Optimized memory usage and cleanup
- ✅ **Debounced Operations** - Smooth user interactions
- ✅ **Loading States** - Progressive loading with fallback mechanisms
- ✅ **Error Handling** - Comprehensive error boundaries

### Styling Features
- ✅ **CoreUI Integration** - Seamless CoreUI styling and components
- ✅ **Theme Support** - Multiple themes (default, dark, compact, minimal)
- ✅ **Professional Design** - Modern, attractive appearance
- ✅ **Smooth Animations** - Enhanced user experience
- ✅ **Custom Styling** - Extensive customization options

### Developer Features
- ✅ **Slot-based Customization** - Custom cell rendering with scoped slots
- ✅ **Event-driven** - Comprehensive event system
- ✅ **TypeScript Ready** - Full prop validation
- ✅ **Plugin Architecture** - Extensible with custom plugins

## 📦 Installation

```bash
# Install the component (if packaged separately)
npm install vue2-coreui-datatable

# Or use directly from the project
import Vue2DataTable from '@/components/common/Vue2DataTable'
```

## 🎯 Quick Start

### Basic Usage

```vue
<template>
  <Vue2DataTable
    :columns="columns"
    :data-source="data"
    :selectable="true"
    @row-click="handleRowClick"
  />
</template>

<script>
import Vue2DataTable from '@/components/common/Vue2DataTable'

export default {
  components: {
    Vue2DataTable
  },
  data() {
    return {
      columns: [
        { key: 'id', label: 'ID', sortable: true },
        { key: 'name', label: 'Name', sortable: true, searchable: true },
        { key: 'email', label: 'Email', sortable: true, searchable: true },
        { key: 'status', label: 'Status', sortable: true }
      ],
      data: [
        { id: 1, name: 'John Doe', email: '<EMAIL>', status: 'Active' },
        { id: 2, name: 'Jane Smith', email: '<EMAIL>', status: 'Inactive' }
      ]
    }
  },
  methods: {
    handleRowClick(payload) {
      console.log('Row clicked:', payload.item)
    }
  }
}
</script>
```

### Advanced Usage with Virtual Scrolling

```vue
<template>
  <Vue2DataTable
    :columns="columns"
    :data-source="largeDataset"
    :virtual-scroll-enabled="true"
    :enable-performance-monitoring="true"
    :row-height="50"
    :column-width="150"
    :selectable="true"
    :show-search="true"
    :show-pagination="false"
    theme="default"
    @performance-update="handlePerformanceUpdate"
  >
    <!-- Custom cell rendering -->
    <template #status="{ value, item }">
      <span :class="getStatusClass(value)">{{ value }}</span>
    </template>

    <template #amount="{ value }">
      <span class="amount-cell">${{ formatCurrency(value) }}</span>
    </template>
  </Vue2DataTable>
</template>
```

### Scroll Sensitivity Configuration

```vue
<template>
  <Vue2DataTable
    :columns="columns"
    :data-source="largeDataset"
    :virtual-scroll-enabled="true"
    :scroll-sensitivity="0.2"
    :smooth-scroll-enabled="true"
    :wheel-scroll-multiplier="0.3"
    :row-height="50"
  >
    <!-- Table content -->
  </Vue2DataTable>
</template>

<script>
export default {
  data() {
    return {
      columns: [
        { key: 'id', label: 'ID' },
        { key: 'name', label: 'Name' },
        { key: 'email', label: 'Email' }
      ],
      largeDataset: [] // Your large dataset
    }
  }
}
</script>
```

## 📋 Props Reference

### Core Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `columns` | Array | **required** | Column definitions |
| `dataSource` | Array\|String\|Function | **required** | Data source (array, URL, or function) |
| `height` | String\|Number | `'auto'` | Table height |
| `theme` | String | `'default'` | Theme variant |

### Feature Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `searchable` | Boolean | `true` | Enable search functionality |
| `sortable` | Boolean | `true` | Enable sorting |
| `selectable` | Boolean | `false` | Enable row selection |
| `showSearch` | Boolean | `true` | Show search bar |
| `showPagination` | Boolean | `true` | Show pagination |
| `showTotalBar` | Boolean | `true` | Show total records bar |

### Virtual Scrolling Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `virtualScrollEnabled` | Boolean | `false` | Force enable virtual scrolling |
| `virtualScrollThreshold` | Number | `1000` | Auto-enable threshold |
| `rowHeight` | Number | `40` | Fixed row height (px) |
| `columnWidth` | Number | `150` | Default column width (px) |
| `bufferSize` | Number | `10` | Virtual scroll buffer size |

### Scroll Sensitivity Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `scrollSensitivity` | Number | `0.3` | Scroll sensitivity (0.1-2.0, lower = less sensitive) |
| `smoothScrollEnabled` | Boolean | `true` | Enable smooth scrolling animation |
| `wheelScrollMultiplier` | Number | `0.5` | Wheel scroll speed multiplier (0.1-2.0) |

### Performance Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `enablePerformanceMonitoring` | Boolean | `false` | Enable performance tracking |
| `freezeData` | Boolean | `false` | Freeze data objects for performance |

## 🎨 Column Configuration

```javascript
const columns = [
  {
    key: 'id',                    // Data property key
    label: 'ID',                  // Display label
    sortable: true,               // Enable sorting
    searchable: true,             // Include in search
    width: '80px',                // Column width
    align: 'center',              // Text alignment
    type: 'number',               // Data type
    fixed: 'left',                // Fixed positioning
    resizable: true,              // Allow resizing
    format: {                     // Data formatting
      type: 'number',
      options: { decimals: 0 }
    }
  }
]
```

### Column Types
- `text` (default)
- `number`
- `date`
- `boolean`
- `currency`
- `percentage`

### Column Alignment
- `left` (default)
- `center`
- `right`

## 🎭 Custom Cell Rendering

Use scoped slots for custom cell content:

```vue
<template>
  <Vue2DataTable :columns="columns" :data-source="data">
    <!-- Status badge -->
    <template #status="{ value, item, column, index }">
      <CBadge :color="getStatusColor(value)">{{ value }}</CBadge>
    </template>
    
    <!-- Progress bar -->
    <template #progress="{ value }">
      <div class="progress">
        <div class="progress-bar" :style="{ width: value + '%' }">
          {{ value }}%
        </div>
      </div>
    </template>
    
    <!-- Action buttons -->
    <template #actions="{ item }">
      <CButton size="sm" color="info" @click="editItem(item)">Edit</CButton>
      <CButton size="sm" color="danger" @click="deleteItem(item)">Delete</CButton>
    </template>
  </Vue2DataTable>
</template>
```

## 📡 Events

| Event | Payload | Description |
|-------|---------|-------------|
| `row-click` | `{ item, index, event }` | Row clicked |
| `row-select` | `{ item, selected }` | Row selection changed |
| `search` | `{ searchTerm, results, stats }` | Search performed |
| `sort` | `{ column, direction, multiSort }` | Sorting changed |
| `page-change` | `pageNumber` | Page changed |
| `page-size-change` | `pageSize` | Page size changed |
| `performance-update` | `{ renderTime, memoryUsage, itemCount }` | Performance metrics |
| `data-loaded` | `{ data, totalRows, duration }` | Data loading completed |
| `error` | `{ error, retryCount, canRetry }` | Error occurred |

## 🎨 Themes

### Available Themes
- `default` - Standard CoreUI theme
- `dark` - Dark mode theme
- `compact` - Compact layout
- `minimal` - Minimal styling

### Custom Themes
Create custom themes by extending the CSS:

```scss
.vue2-datatable--custom {
  --dt-primary-color: #your-color;
  --dt-border-color: #your-border;
  --dt-hover-color: #your-hover;
  
  .table-container-vue2 {
    background: your-background;
  }
}
```

## ⚡ Performance Optimization

### Virtual Scrolling
Automatically enabled for datasets > 1,000 items:

```vue
<Vue2DataTable
  :data-source="largeDataset"
  :virtual-scroll-enabled="true"
  :row-height="50"
  :buffer-size="10"
/>
```

### Memory Management
- Object freezing for immutable data
- Automatic cleanup on component destroy
- Debounced search and scroll operations
- Efficient DOM recycling

### Performance Monitoring
Enable to track performance metrics:

```vue
<Vue2DataTable
  :enable-performance-monitoring="true"
  @performance-update="handlePerformanceUpdate"
/>
```

## 🔧 Advanced Configuration

### Server-side Data
```vue
<Vue2DataTable
  :data-source="'/api/users'"
  :request-headers="{ 'Authorization': 'Bearer token' }"
  :auto-retry="true"
  server-side-pagination
/>
```

### Function Data Source
```vue
<Vue2DataTable
  :data-source="loadData"
/>

<script>
export default {
  methods: {
    async loadData(params) {
      const response = await api.get('/users', { params })
      return {
        data: response.data.items,
        total: response.data.total
      }
    }
  }
}
</script>
```

## 🐛 Troubleshooting

### Common Issues

1. **Performance Issues**
   - Enable virtual scrolling for large datasets
   - Use fixed row heights
   - Limit searchable columns

2. **Memory Leaks**
   - Enable `freezeData` for immutable data
   - Ensure proper component cleanup

3. **Styling Issues**
   - Check CoreUI CSS is loaded
   - Verify theme compatibility

## 📄 License

This component is part of the CoreUI Pro Vue Laravel Admin Template.

## 🤝 Contributing

Please follow the existing code style and add tests for new features.

## 📞 Support

For support, please refer to the CoreUI documentation or create an issue in the project repository.
