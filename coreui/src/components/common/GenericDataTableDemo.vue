<template>
  <div class="demo-container">
    <h2>GenericDataTable Performance Demo</h2>
    
    <!-- Dataset Size Controls -->
    <div class="demo-controls">
      <div class="control-group">
        <label>Dataset Size:</label>
        <select v-model="selectedDatasetSize" @change="generateData">
          <option value="100">100 records</option>
          <option value="1000">1,000 records</option>
          <option value="10000">10,000 records</option>
          <option value="50000">50,000 records</option>
          <option value="100000">100,000 records</option>
          <option value="500000">500,000 records</option>
        </select>
      </div>
      
      <div class="control-group">
        <label>Column Count:</label>
        <select v-model="selectedColumnCount" @change="generateColumns">
          <option value="5">5 columns</option>
          <option value="10">10 columns</option>
          <option value="20">20 columns</option>
          <option value="40">40 columns</option>
          <option value="60">60 columns</option>
        </select>
      </div>
      
      <div class="control-group">
        <label>
          <input type="checkbox" v-model="forceVirtualScrolling" />
          Force Virtual Scrolling
        </label>
      </div>
      
      <div class="control-group">
        <label>
          <input type="checkbox" v-model="enablePerformanceMonitoring" />
          Performance Monitoring
        </label>
      </div>
    </div>

    <!-- Performance Stats -->
    <div class="performance-stats" v-if="performanceStats.renderTime > 0">
      <div class="stat">
        <span class="stat-label">Last Render:</span>
        <span class="stat-value">{{ performanceStats.renderTime }}ms</span>
      </div>
      <div class="stat">
        <span class="stat-label">Memory Usage:</span>
        <span class="stat-value">{{ performanceStats.memoryUsage }}MB</span>
      </div>
      <div class="stat">
        <span class="stat-label">Mode:</span>
        <span class="stat-value">{{ performanceStats.mode }}</span>
      </div>
    </div>

    <!-- Optimized Data Table -->
    <GenericDataTable
      :items="demoData"
      :columns="demoColumns"
      :enable-virtual-scrolling="forceVirtualScrolling || null"
      :enable-performance-monitoring="enablePerformanceMonitoring"
      :selectable="true"
      :show-search="true"
      :show-pagination="true"
      :show-total-bar="true"
      :row-height="50"
      :column-width="150"
      search-placeholder="Search demo data..."
      @performance-update="handlePerformanceUpdate"
    >
      <!-- Custom slot for status column -->
      <template #status="{ value }">
        <span :class="getStatusClass(value)">{{ value }}</span>
      </template>
      
      <!-- Custom slot for amount column -->
      <template #amount="{ value }">
        <span class="amount-cell">${{ formatNumber(value) }}</span>
      </template>
    </GenericDataTable>
  </div>
</template>

<script>
import GenericDataTable from './GenericDataTable.vue';

export default {
  name: 'GenericDataTableDemo',
  components: {
    GenericDataTable
  },
  
  data() {
    return {
      selectedDatasetSize: 1000,
      selectedColumnCount: 10,
      forceVirtualScrolling: false,
      enablePerformanceMonitoring: true,
      demoData: [],
      demoColumns: [],
      performanceStats: {
        renderTime: 0,
        memoryUsage: 0,
        mode: 'Pagination'
      }
    };
  },
  
  mounted() {
    this.generateColumns();
    this.generateData();
  },
  
  methods: {
    generateColumns() {
      const baseColumns = [
        { key: 'id', label: 'ID', type: 'number', sortable: true },
        { key: 'name', label: 'Name', type: 'text', sortable: true },
        { key: 'email', label: 'Email', type: 'text', sortable: true },
        { key: 'status', label: 'Status', type: 'text', sortable: true },
        { key: 'amount', label: 'Amount', type: 'currency', sortable: true },
        { key: 'date', label: 'Date', type: 'date', sortable: true },
        { key: 'category', label: 'Category', type: 'text', sortable: true },
        { key: 'priority', label: 'Priority', type: 'text', sortable: true },
        { key: 'description', label: 'Description', type: 'text', sortable: true },
        { key: 'location', label: 'Location', type: 'text', sortable: true }
      ];
      
      // Generate additional columns if needed
      const columns = [...baseColumns];
      for (let i = baseColumns.length; i < this.selectedColumnCount; i++) {
        columns.push({
          key: `field_${i + 1}`,
          label: `Field ${i + 1}`,
          type: 'text',
          sortable: true
        });
      }
      
      this.demoColumns = columns.slice(0, this.selectedColumnCount);
    },
    
    generateData() {
      const statuses = ['Active', 'Inactive', 'Pending', 'Completed', 'Cancelled'];
      const categories = ['Sales', 'Marketing', 'Support', 'Development', 'HR'];
      const priorities = ['Low', 'Medium', 'High', 'Critical'];
      const locations = ['New York', 'London', 'Tokyo', 'Sydney', 'Berlin'];
      
      const data = [];
      const startTime = performance.now();
      
      for (let i = 1; i <= this.selectedDatasetSize; i++) {
        const item = {
          id: i,
          name: `User ${i}`,
          email: `user${i}@example.com`,
          status: statuses[Math.floor(Math.random() * statuses.length)],
          amount: Math.floor(Math.random() * 10000) + 100,
          date: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString(),
          category: categories[Math.floor(Math.random() * categories.length)],
          priority: priorities[Math.floor(Math.random() * priorities.length)],
          description: `Description for item ${i}`,
          location: locations[Math.floor(Math.random() * locations.length)]
        };
        
        // Add additional fields if needed
        for (let j = 11; j <= this.selectedColumnCount; j++) {
          item[`field_${j}`] = `Value ${j}-${i}`;
        }
        
        data.push(item);
      }
      
      const endTime = performance.now();
      console.log(`Generated ${this.selectedDatasetSize} records in ${(endTime - startTime).toFixed(2)}ms`);
      
      this.demoData = data;
    },
    
    handlePerformanceUpdate(stats) {
      this.performanceStats = stats;
    },
    
    getStatusClass(status) {
      const classes = {
        'Active': 'status-active',
        'Inactive': 'status-inactive',
        'Pending': 'status-pending',
        'Completed': 'status-completed',
        'Cancelled': 'status-cancelled'
      };
      return classes[status] || 'status-default';
    },
    
    formatNumber(value) {
      return new Intl.NumberFormat().format(value);
    }
  }
};
</script>

<style scoped>
.demo-container {
  padding: 20px;
  max-width: 100%;
}

.demo-controls {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  flex-wrap: wrap;
  padding: 16px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.control-group label {
  font-weight: 600;
  color: #4a5568;
  font-size: 14px;
}

.control-group select {
  padding: 8px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  background: white;
}

.performance-stats {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  padding: 12px 16px;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
  border-radius: 8px;
  border: 1px solid rgba(102, 126, 234, 0.2);
}

.stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.stat-label {
  font-size: 12px;
  color: #718096;
  font-weight: 500;
}

.stat-value {
  font-size: 14px;
  color: #2d3748;
  font-weight: 700;
}

/* Status styling */
.status-active { color: #10b981; font-weight: 600; }
.status-inactive { color: #6b7280; font-weight: 600; }
.status-pending { color: #f59e0b; font-weight: 600; }
.status-completed { color: #3b82f6; font-weight: 600; }
.status-cancelled { color: #ef4444; font-weight: 600; }

.amount-cell {
  font-weight: 600;
  color: #059669;
}
</style>
