<template>
  <c-card>
    <c-card-header>Employee Kpis Report</c-card-header>
    <c-card-body>
      <div class="row">
        <div class="col-lg-3 col-md-3 col-sm-8">
          <c-input label="From" type="date" placeholder="From" v-model="from_date"></c-input>
        </div>

        <div class="col-lg-3 col-md-3 col-sm-8">
          <c-input label="To" type="date" placeholder="To" v-model="to_date" @input="getAllData"></c-input>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-8">
          <c-form-group>
            <template #label> Line </template>
            <template #input>
              <input label="All" v-if="lines.length != 0" id="line" class="m-1" type="checkbox" v-model="checkAllLines"
                title="Check All lines" @change="checkAllLine" />
              <label for="line" v-if="lines.length != 0" style="font-weight: bold">All</label>
              <v-select v-model="line_id" :options="lines" label="name" :value="0" :reduce="(line) => line.id"
                placeholder="Select Lines" class="mt-2" multiple @input="getLineData" />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-8">
          <c-form-group>
            <template #label> Position </template>
            <template #input>
              <v-select v-model="position" :options="positions" label="name" placeholder="Select Position" class="mt-2"
                @input="getUsersPosition" />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-4 col-md-4 col-sm-8">
          <c-form-group>
            <template #label> Employee </template>
            <template #input>
              <input label="All" v-if="users.length != 0" id="user" class="m-1" type="checkbox" v-model="checkAllUsers"
                title="Check All Users" @change="checkAllEmployees" />
              <label for="user" v-if="users.length != 0" style="font-weight: bold">All</label>
              <v-select v-model="user_id" :options="users" label="fullname" :value="0" :reduce="(user) => user.id"
                placeholder="Select Employee" class="mt-2" multiple />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-4 col-md-4 col-sm-8">
          <c-form-group>
            <template #label>
              <strong>Emp Code</strong>
            </template>
            <template #input>
              <input v-if="codes.length != 0" class="m-1" id="codes" type="checkbox" v-model="checkAllCodes"
                title="Check All Employees" @change="checkAllCode" />
              <label for="codes" v-if="codes.length != 0" style="font-weight: bold">All</label>
              <v-select v-model="codes_id" :options="codes" multiple :reduce="(user) => user.id" label="emp_code"
                :value="0" placeholder="Select Code" class="mt-2" />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-4 col-md-4 col-sm-8">
          <c-form-group>
            <template #label> View </template>
            <template #input>
              <v-select v-model="type" :options="views" label="name" placeholder="Select View" class="mt-2" />
            </template>
          </c-form-group>
        </div>
      </div>
    </c-card-body>
    <c-card-footer>
      <c-button v-if="line_id.length > 0" color="primary" class="text-white" @click="show"
        style="float: right">Show</c-button>
    </c-card-footer>
  </c-card>
</template>
<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
import moment from "moment";

export default {
  components: {
    vSelect,
  },
  emits: ["getSchedule"],
  data() {
    return {
      user_id: [],
      codes_id: [],
      codes: [],
      line_id: [],
      positions: [],
      position: null,
      lines: [],
      views: ["Standard", "Advanced"],
      users: [],
      type: "Advanced",
      checkAllDivisions: false,
      checkAllLines: false,
      checkAllUsers: false,
      checkAllCodes: false,
      from_date: moment().startOf('month').format("YYYY-MM-DD"),
      to_date: moment().endOf('month').format("YYYY-MM-DD"),
    };
  },
  methods: {
    initialize() {
      axios
        .post("/api/get-lines-with-dates", {
          from: null,
          to: null,
          data: ['lines', 'users', 'roles', 'shifts']
        })
        .then((response) => {
          this.lines = response.data.data.lines;
          this.users = response.data.data.users;
          this.shifts = response.data.data.shifts;
          this.shift_id = this.shifts.map((item) => item.id);
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getAllData() {
      this.line_id = [];
      this.user_id = [];
      this.codes_id = [];
      this.initialize();
    },
    getLineData() {
      axios
        .post(`/api/get-lines-data-with-dates`, {
          lines: this.line_id,
          from: this.from_date,
          to: this.to_date,
          data: ['users', 'codes']
        })
        .then((response) => {
          this.users = response.data.data.users;
          this.codes = response.data.data.codes;
          this.getpositions();
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getpositions() {
      axios
        .post("/api/get-positions", {
          lines: this.line_id,
          from: this.from_date,
          to: this.to_date,
        })
        .then((res) => {
          this.positions = res.data.data;
        })
        .catch((err) => {
          this.showErrorMessage(err);
        });
    },
    getUsersPosition() {

      axios
        .post("/api/position-users", {
          lines: this.line_id,
          role_id: this.position.id.split("_")[0],
          role_type: this.position.id.split("_")[1],
          from: this.from_date,
          to: this.to_date,
        })
        .then((res) => {
          this.users = res.data.users;
          this.codes = res.data.codes;
        })
        .catch((err) => {
          this.showErrorMessage(err);
        });
    },
    checkAllLine() {
      if (this.checkAllLines) this.line_id = this.lines.map((item) => item.id);
      if (this.checkAllLines == false) this.line_id = [];
      this.getLineData();
    },
    checkAllEmployees() {
      if (this.checkAllUsers) this.user_id = this.users.map((item) => item.id);
      if (this.checkAllUsers == false) this.user_id = [];
    },
    checkAllCode() {
      if (this.checkAllCodes) this.codes_id = this.users.map((item) => item.id);
      if (this.checkAllCodes == false) this.codes_id = [];
    },
    show() {
      let kpisFilter = {
        lines: this.line_id,
        users: this.user_id,
        codes: this.codes_id,
        position: this.position,
        type: this.type,
        fromDate: this.from_date,
        toDate: this.to_date,
      };
      this.$emit("getSchedule", { kpisFilter });
    },
  },
  created() {
    this.initialize();
  },
};
</script>