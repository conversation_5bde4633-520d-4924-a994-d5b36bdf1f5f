<template>
  <c-card>
    <c-card-header>Employee Tracking Report</c-card-header>
    <c-card-body>
      <div class="row">
        <div class="col-lg-2 col-md-2 col-sm-8">
          <c-form-group>
            <template #label> Line </template>
            <template #input>
              <input label="All" v-if="lines.length != 0" id="line" class="m-1" type="checkbox" v-model="checkAllLines"
                title="Check All lines" @change="checkAllLine" />
              <label for="line" v-if="lines.length != 0" style="font-weight: bold">All</label>
              <v-select v-model="line_id" :options="lines" label="name" :value="0" :reduce="(line) => line.id"
                placeholder="Select Line" class="mt-2" multiple @input="getLineData" />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-8">
          <c-form-group>
            <template #label> Employee </template>
            <template #input>
              <input label="All" v-if="users.length != 0" id="user" class="m-1" type="checkbox" v-model="checkAllUsers"
                title="Check All Users" @change="checkAllEmployees" />
              <label for="user" v-if="users.length != 0" style="font-weight: bold">All</label>
              <v-select v-model="user_id" :options="users" label="fullname" :value="0" :reduce="(user) => user.id"
                placeholder="Select Employee" class="mt-2" multiple />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-8">
          <c-form-group>
            <template #label> Shift </template>
            <template #input>
              <input label="All" id="shift" v-if="shifts.length != 0" class="m-1" type="checkbox"
                v-model="checkAllShifts" title="Check All Shifts" @change="checkAllShift" />
              <label for="shift" v-if="shifts.length != 0" style="font-weight: bold">All</label>
              <v-select v-model="shift_id" :options="shifts" label="name" placeholder="Select Shift"
                :reduce="(option) => option.id" class="mt-2" multiple />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-2 col-md-2 col-sm-8">
          <c-input label="From" type="date" placeholder="From" v-model="from_date"></c-input>
        </div>

        <div class="col-lg-2 col-md-2 col-sm-8">
          <c-input label="To" type="date" placeholder="To" v-model="to_date"></c-input>
        </div>
      </div>

      <!-- No Show Data Filter Row -->
      <div class="row mt-3">
        <div class="col-lg-12">
          <div class="no-show-filter-container">
            <c-form-group class="no-show-filter-group">
              <template #label>
                <span class="filter-label">
                  <i class="cil-warning text-danger mr-2"></i>
                  No Show Data Filter
                </span>
              </template>
              <template #input>
                <div class="no-show-controls">
                  <!-- Toggle Switch Style -->
                  <div class="custom-control custom-switch no-show-switch">
                    <input
                      type="checkbox"
                      class="custom-control-input"
                      id="noShowToggle"
                      v-model="includeNoShowData"
                      @change="handleNoShowToggle"
                    >
                    <label class="custom-control-label" for="noShowToggle">
                      <span class="switch-text">
                        {{ includeNoShowData ? 'Show Only No-Show Data' : 'Show All Data' }}
                      </span>
                    </label>
                  </div>

                  <!-- Info Badge -->
                  <div class="no-show-info">
                    <span class="badge" :class="includeNoShowData ? 'badge-danger' : 'badge-success'">
                      <i class="cil-info mr-1"></i>
                      {{ includeNoShowData ? 'Filtering: No-Show Only' : 'Showing: All Data' }}
                    </span>
                  </div>

                  <!-- Description -->
                  <small class="form-text text-muted no-show-description">
                    <i class="cil-lightbulb mr-1"></i>
                    {{ includeNoShowData
                      ? 'Only dates with employee no-show incidents will be displayed. All other data (visits, office work, vacations, etc.) will be hidden.'
                      : 'All employee tracking data will be displayed including visits, office work, vacations, off days, holidays, and no-show incidents.'
                    }}
                  </small>
                </div>
              </template>
            </c-form-group>
          </div>
        </div>
      </div>
    </c-card-body>
    <c-card-footer>
      <c-button v-if="line_id != null" color="primary" class="text-white" @click="show"
        style="float: right">Show</c-button>
    </c-card-footer>
  </c-card>
</template>
<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  components: {
    vSelect,
  },
  emits: ["getSchedule", "noShowFilterChanged"],
  data() {
    return {
      user_id: [],
      line_id: [],
      lines: [],
      shift_id: [],
      shifts: [],
      users: [],
      shift_id: [],
      checkAllDivisions: false,
      checkAllShifts: true,
      checkAllLines: false,
      checkAllUsers: false,
      from_date: new Date().toISOString().slice(0, 10),
      to_date: new Date().toISOString().slice(0, 10),
      // No Show Data filter - disabled by default as per requirements
      includeNoShowData: false,
    };
  },
  methods: {
    initialize() {
      axios
        .post("/api/get-lines-with-dates", {
          from: null,
          to: null,
          data: ['lines', 'users', 'shifts']
        })
        .then((response) => {
          this.lines = response.data.data.lines;
          this.users = response.data.data.users;
          this.shifts = response.data.data.shifts;
          this.shift_id = this.shifts.map((item) => item.id);
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getLineData() {
      axios
        .post(`/api/visitsReport`, { lines: this.line_id })
        .then((response) => {
          this.users = response.data.users;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    checkAllLine() {
      if (this.checkAllLines) this.line_id = this.lines.map((item) => item.id);
      if (this.checkAllLines == false) this.line_id = null;
      this.getLineData();
    },
    checkAllEmployees() {
      if (this.checkAllUsers) this.user_id = this.users.map((item) => item.id);
      if (this.checkAllUsers == false) this.user_id = null;
    },
    checkAllShift() {
      if (this.checkAllShifts) {
        this.shift_id = this.shifts.map((item) => item.id);
        this.checkAllAccounts();
      }
      if (this.checkAllShifts == false) this.shift_id = null;
    },
    handleNoShowToggle() {
      // Emit event to notify parent component about filter change
      this.$emit('noShowFilterChanged', this.includeNoShowData);

      // Log the change for debugging
      console.log('No Show Data filter changed:',
        this.includeNoShowData ? 'Show Only No-Show Data' : 'Show All Data');
    },
    show() {
      let trackingFilter = {
        lines: this.line_id,
        users: this.user_id,
        shifts: this.shift_id,
        fromDate: this.from_date,
        toDate: this.to_date,
        // Include the no-show filter in the tracking filter
        includeNoShowData: this.includeNoShowData,
      };
      this.$emit("getSchedule", { trackingFilter });
    },
  },
  created() {
    this.initialize();
  },
};
</script>

<style scoped>
/* No Show Data Filter Styles */
.no-show-filter-container {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
  margin-top: 10px;
}

.no-show-filter-group {
  margin-bottom: 0;
}

.filter-label {
  font-weight: 600;
  color: #495057;
  font-size: 14px;
  display: flex;
  align-items: center;
}

.no-show-controls {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* Custom Switch Styling */
.no-show-switch {
  display: flex;
  align-items: center;
  margin-bottom: 0;
}

.no-show-switch .custom-control-input:checked ~ .custom-control-label::before {
  background-color: #dc3545;
  border-color: #dc3545;
}

.no-show-switch .custom-control-input:focus ~ .custom-control-label::before {
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.no-show-switch .custom-control-label {
  padding-left: 0.5rem;
  font-weight: 500;
  color: #495057;
}

.switch-text {
  font-size: 14px;
  margin-left: 8px;
}

/* Info Badge Styling */
.no-show-info {
  display: flex;
  align-items: center;
}

.no-show-info .badge {
  font-size: 12px;
  padding: 6px 12px;
  border-radius: 20px;
  font-weight: 500;
  display: flex;
  align-items: center;
}

.badge-danger {
  background-color: #dc3545;
  color: white;
}

.badge-success {
  background-color: #28a745;
  color: white;
}

.badge-secondary {
  background-color: #6c757d;
  color: white;
}

/* Description Styling */
.no-show-description {
  font-size: 12px;
  color: #6c757d;
  line-height: 1.4;
  display: flex;
  align-items: flex-start;
  margin-top: 4px;
}

.no-show-description i {
  margin-top: 2px;
  flex-shrink: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .no-show-filter-container {
    padding: 15px;
  }

  .no-show-controls {
    gap: 10px;
  }

  .filter-label {
    font-size: 13px;
  }

  .switch-text {
    font-size: 13px;
  }

  .no-show-info .badge {
    font-size: 11px;
    padding: 4px 8px;
  }

  .no-show-description {
    font-size: 11px;
  }
}

/* Animation for smooth transitions */
.no-show-filter-container {
  transition: all 0.3s ease;
}

.no-show-info .badge {
  transition: all 0.3s ease;
}

.custom-control-input:checked ~ .custom-control-label::before {
  transition: all 0.3s ease;
}

/* Focus states for accessibility */
.custom-control-input:focus ~ .custom-control-label::before {
  outline: none;
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

/* Hover effects */
.no-show-switch:hover .custom-control-label {
  color: #dc3545;
}

.no-show-switch:hover .custom-control-label::before {
  border-color: #dc3545;
}
</style>