/**
 * Excel Debug Utilities
 * Provides debugging and troubleshooting tools for Excel download functionality
 */

export class ExcelDebugUtils {
    static enableDebugMode() {
        localStorage.setItem('excel-debug-mode', 'true');
        console.log('🔧 Excel debug mode enabled');
        console.log('Available debug commands:');
        console.log('- ExcelDebugUtils.getSystemInfo()');
        console.log('- ExcelDebugUtils.getErrorLog()');
        console.log('- ExcelDebugUtils.testWebWorker()');
        console.log('- ExcelDebugUtils.clearErrorLog()');
    }

    static disableDebugMode() {
        localStorage.removeItem('excel-debug-mode');
        console.log('🔧 Excel debug mode disabled');
    }

    static isDebugMode() {
        return localStorage.getItem('excel-debug-mode') === 'true';
    }

    static getSystemInfo() {
        const memory = performance.memory;
        const info = {
            browser: {
                userAgent: navigator.userAgent,
                language: navigator.language,
                cookieEnabled: navigator.cookieEnabled,
                onLine: navigator.onLine,
                webWorkerSupported: typeof Worker !== 'undefined'
            },
            memory: memory ? {
                used: Math.round(memory.usedJSHeapSize / 1024 / 1024) + 'MB',
                total: Math.round(memory.totalJSHeapSize / 1024 / 1024) + 'MB',
                limit: Math.round(memory.jsHeapSizeLimit / 1024 / 1024) + 'MB',
                usage: Math.round((memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100) + '%'
            } : 'Not available',
            performance: {
                now: performance.now ? performance.now() : 'Not available',
                timeOrigin: performance.timeOrigin || 'Not available'
            },
            screen: {
                width: screen.width,
                height: screen.height,
                colorDepth: screen.colorDepth
            },
            timestamp: new Date().toISOString()
        };

        console.group('📊 Excel System Information');
        console.table(info.browser);
        console.log('Memory:', info.memory);
        console.log('Performance:', info.performance);
        console.log('Screen:', info.screen);
        console.groupEnd();

        return info;
    }

    static async testWebWorker() {
        console.group('🔧 Web Worker Test');
        
        if (typeof Worker === 'undefined') {
            console.error('❌ Web Workers not supported');
            console.groupEnd();
            return { supported: false, error: 'Web Workers not supported' };
        }

        const workerPaths = [
            '/workers/ExcelProcessingWorker.js',
            './workers/ExcelProcessingWorker.js',
            '../workers/ExcelProcessingWorker.js',
            '/public/workers/ExcelProcessingWorker.js'
        ];

        const results = [];

        for (const path of workerPaths) {
            try {
                console.log(`Testing worker path: ${path}`);
                
                const worker = new Worker(path);
                const testResult = await new Promise((resolve) => {
                    const timeout = setTimeout(() => {
                        resolve({ success: false, error: 'Timeout' });
                    }, 5000);

                    worker.onmessage = (e) => {
                        clearTimeout(timeout);
                        resolve({ success: true, message: e.data });
                    };

                    worker.onerror = (error) => {
                        clearTimeout(timeout);
                        resolve({ success: false, error: error.message });
                    };

                    // Send test message
                    worker.postMessage({ type: 'TEST' });
                });

                worker.terminate();
                
                results.push({
                    path,
                    ...testResult
                });

                if (testResult.success) {
                    console.log(`✅ ${path} - Working`);
                } else {
                    console.warn(`❌ ${path} - ${testResult.error}`);
                }

            } catch (error) {
                console.warn(`❌ ${path} - ${error.message}`);
                results.push({
                    path,
                    success: false,
                    error: error.message
                });
            }
        }

        console.groupEnd();
        return results;
    }

    static getErrorLog() {
        try {
            const errorLog = JSON.parse(localStorage.getItem('excel-error-log') || '[]');
            console.group('📋 Excel Error Log');
            
            if (errorLog.length === 0) {
                console.log('No errors logged');
            } else {
                console.log(`Found ${errorLog.length} error(s):`);
                errorLog.forEach((error, index) => {
                    console.group(`Error ${index + 1} - ${error.timestamp}`);
                    console.log('Message:', error.message);
                    console.log('Item Count:', error.itemCount);
                    console.log('Memory Usage:', error.memoryUsageMB + 'MB');
                    console.log('Browser:', error.browserInfo);
                    console.groupEnd();
                });
            }
            
            console.groupEnd();
            return errorLog;
        } catch (error) {
            console.error('Could not retrieve error log:', error);
            return [];
        }
    }

    static clearErrorLog() {
        localStorage.removeItem('excel-error-log');
        console.log('🗑️ Excel error log cleared');
    }

    static exportErrorLog() {
        const errorLog = this.getErrorLog();
        const dataStr = JSON.stringify(errorLog, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        
        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = `excel-error-log-${new Date().toISOString().split('T')[0]}.json`;
        link.click();
        
        console.log('📁 Error log exported');
    }

    static simulateMemoryPressure(sizeMB = 100) {
        console.warn(`⚠️ Simulating memory pressure: ${sizeMB}MB`);
        
        const arrays = [];
        const arraySize = 1024 * 1024; // 1MB per array
        
        try {
            for (let i = 0; i < sizeMB; i++) {
                arrays.push(new Array(arraySize).fill(Math.random()));
                
                if (i % 10 === 0) {
                    console.log(`Allocated ${i}MB...`);
                }
            }
            
            console.log(`✅ Successfully allocated ${sizeMB}MB`);
            
            // Hold for 5 seconds then release
            setTimeout(() => {
                arrays.length = 0;
                if (window.gc) window.gc();
                console.log('🗑️ Memory released');
            }, 5000);
            
        } catch (error) {
            console.error('❌ Memory allocation failed:', error.message);
            arrays.length = 0;
        }
    }

    static benchmarkDataProcessing(recordCount = 10000) {
        console.group(`⏱️ Benchmarking data processing with ${recordCount.toLocaleString()} records`);
        
        const startTime = performance.now();
        const startMemory = performance.memory ? performance.memory.usedJSHeapSize : 0;
        
        // Generate test data
        const data = [];
        for (let i = 0; i < recordCount; i++) {
            data.push({
                id: i,
                name: `Record ${i}`,
                value: Math.random() * 1000,
                date: new Date().toISOString(),
                category: `Category ${i % 10}`
            });
        }
        
        const generationTime = performance.now() - startTime;
        const generationMemory = performance.memory ? performance.memory.usedJSHeapSize - startMemory : 0;
        
        // Simulate processing
        const processStart = performance.now();
        let processedCount = 0;
        
        data.forEach(record => {
            // Simulate some processing
            Object.keys(record).forEach(key => {
                const value = record[key];
                if (typeof value === 'string') {
                    value.toUpperCase();
                }
            });
            processedCount++;
        });
        
        const processingTime = performance.now() - processStart;
        const totalTime = performance.now() - startTime;
        const endMemory = performance.memory ? performance.memory.usedJSHeapSize : 0;
        
        const results = {
            recordCount,
            generationTime: Math.round(generationTime),
            processingTime: Math.round(processingTime),
            totalTime: Math.round(totalTime),
            recordsPerSecond: Math.round(recordCount / (totalTime / 1000)),
            memoryUsed: Math.round((endMemory - startMemory) / 1024 / 1024),
            memoryPerRecord: Math.round((endMemory - startMemory) / recordCount)
        };
        
        console.table(results);
        console.groupEnd();
        
        return results;
    }

    static getRecommendations() {
        const systemInfo = this.getSystemInfo();
        const recommendations = [];
        
        if (!systemInfo.browser.webWorkerSupported) {
            recommendations.push('❌ Web Workers not supported - Large datasets will process on main thread');
        }
        
        if (systemInfo.memory === 'Not available') {
            recommendations.push('⚠️ Memory monitoring not available - Cannot detect memory issues');
        } else {
            const usagePercent = parseInt(systemInfo.memory.usage);
            if (usagePercent > 80) {
                recommendations.push('⚠️ High memory usage detected - Consider closing other tabs');
            }
        }
        
        if (systemInfo.browser.userAgent.includes('Mobile')) {
            recommendations.push('📱 Mobile browser detected - Limit dataset size to < 10K records');
        }
        
        if (recommendations.length === 0) {
            recommendations.push('✅ System appears capable of handling large datasets');
        }
        
        console.group('💡 Recommendations');
        recommendations.forEach(rec => console.log(rec));
        console.groupEnd();
        
        return recommendations;
    }
}

// Make available globally for console debugging
if (typeof window !== 'undefined') {
    window.ExcelDebugUtils = ExcelDebugUtils;
}

export default ExcelDebugUtils;
