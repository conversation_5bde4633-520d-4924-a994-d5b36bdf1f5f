<template>
  <CRow>
    <CCol col="12" xl="12">
      <transition name="slide">
        <CCard>
          <CCardHeader> Accounts</CCardHeader>
          <CCardBody>
            <CButton class="text-white" color="primary" v-if="checkPermission('create_accounts')"
              :to="{ name: 'CreateAccount' }">Create Account
            </CButton>
            <CDropdown placement="bottom-end" toggler-text="Tools" color="primary"
              class="float-right text-white d-inline-block">
              <CDropdownItem v-if="checkPermission('download_all_templates')"
                @click="templateDownload('Account_Template.xlsx')">Template
              </CDropdownItem>
              <c-dropdown-item v-if="checkPermission('import_accounts')" @click="successModal = true">Upload New
              </c-dropdown-item>

              <c-dropdown-item v-if="checkPermission('import_bulk_edit')" @click="updateModal = true">Bulk Edit
              </c-dropdown-item>

              <c-dropdown-divider />
              <CDropdownItem v-if="checkPermission('export_xlsx_accounts')" @click="exportAccount()">Export to Excel
              </CDropdownItem>
              <CDropdownItem v-if="checkPermission('export_csv_accounts')" @click="exportCSV()">Export to CSV
              </CDropdownItem>
              <CDropdownItem v-if="checkPermission('export_pdf_accounts')" @click="exportAccountPDF()">Export to PDF
              </CDropdownItem>
              <CDropdownItem v-if="checkPermission('export_email_accounts')" @click="sendModal = true">Send to Mail
              </CDropdownItem>
              <hr />
              <CDropdownItem v-if="checkPermission('edit_accounts')" :to="{ name: 'merge-accounts' }">Merge Accounts
              </CDropdownItem>
            </CDropdown>

            <CModal title="Upload Account" color="success" :show.sync="successModal">
              <CInputFile type="file" ref="file" id="file" name="file_name" v-on:change="handleFileUpload"
                placeholder="New file" />
              <CProgress :value="uploadPercentage" color="success" animated showPercentage show-value
                style="height: 15px" class="mt-1" :max="100" v-show="progressBar" />
              <template #footer>
                <CButton class="text-white" @click="successModal = false" color="danger">Discard
                </CButton>
                <CButton class="text-white" @click="importAccount()" color="success">Upload
                </CButton>
              </template>
            </CModal>

            <!-- bulk edit - update modal -->
            <c-modal title="Bulk Edit Accounts" color="success" :show.sync="updateModal">
              <c-input-file type="file" ref="file" id="bulkEditFile" name="file_name" v-on:change="handleFileUpload"
                placeholder="New file" />
              <c-progress :value="uploadPercentage" color="success" animated showPercentage show-value
                style="height: 15px" class="mt-1" :max="100" v-show="progressBar" />
              <template #footer>
                <c-button @click="updateModal = false" color="danger">Discard
                </c-button>
                <c-button @click="importUpdateAccount()" class="text-white" color="success">Upload
                </c-button>
              </template>
            </c-modal>

            <CModal title="Compose" color="success" :show.sync="sendModal">
              <template>
                <div class="form-group">
                  <vue-tags-input v-model="email" :tags="emails" name="email[]" :validation="validation"
                    placeholder="To" :add-on-key="addOnKey" @tags-changed="(newEmails) => (emails = newEmails)" />
                </div>
              </template>

              <CTextarea label="Message:" type="text" name="text" v-model="text" placeholder="Message"></CTextarea>
              <template #footer>
                <CButton @click="sendModal = false" color="danger" class="text-white">Discard
                </CButton>
                <CButton @click="sendAccountMail()" color="success" class="text-white">Send
                </CButton>
              </template>
            </CModal>

            <assign-account-location ref="assignAccountLocation"></assign-account-location>
            <CDataTable hover striped sorter footer itemsPerPageSelect :items="items" :fields="fields"
              :items-per-page="1000" :active-page="1" :responsive="true" thead-top>
              <template slot="cleaner">
                <label class="mfe-2">Filter: </label>
                <c-input v-model="search" placeholder="type string..." type="text" />
                <!-- @change="getData" -->
              </template>
              <template slot="thead-top">
                <td style="border-top: none"><strong>Total</strong></td>
                <td style="border-top: none" class="text-xs-right">
                  {{ totalData }}
                </td>
              </template>
              <template #name="{ item }">
                <td>
                  <strong style="color: blue; cursor: pointer"><a
                      @click="$root.$account('Account Data', item.id)">{{
                        item.name
                      }}</a></strong>
                </td>
              </template>
              <template #actions="{ item }">
                <td>
                  <c-dropdown placement="bottom-end" toggler-text="Actions" color="primary" class="m-2">
                    <c-dropdown-item v-if="checkPermission('show_single_accounts')"
                      @click="$router.push({ name: 'Account', params: { id: item.id } })">
                      <div class="dd-item">
                        <c-icon name="cil-pencil" />
                        <span class="dd-item-text">Show</span>
                      </div>
                    </c-dropdown-item>
                    <c-dropdown-item v-if="checkPermission('edit_accounts')"
                      @click="$router.push({ name: 'EditAccount', params: { id: item.id } })">
                      <div class="dd-item">
                        <c-icon name="cil-magnifying-glass" />
                        <span class="dd-item-text">Edit</span>
                      </div>
                    </c-dropdown-item>
                    <c-dropdown-item v-if="checkPermission('delete_accounts')" @click="
                      $root
                        .$confirm(
                          'Delete',
                          'Do you want to delete this record?',
                          {
                            color: 'red',
                            width: 290,
                            zIndex: 200,
                          }
                        )
                        .then((confirmed) => {
                          if (confirmed) {
                            deleteAccount(item);
                          }
                        })
                      ">
                      <div class="dd-item">
                        <c-icon name="cil-trash" />
                        <span class="dd-item-text">Delete</span>
                      </div>
                    </c-dropdown-item>
                    <c-dropdown-item @click="openAssignLocation(item)">
                      <div class="dd-item">
                        <c-icon name="cil-location-pin" />
                        <span class="dd-item-text">Assign Location</span>
                      </div>
                    </c-dropdown-item>
                  </c-dropdown>
                </td>
              </template>
            </CDataTable>
            <c-pagination v-if="items.length != 0" :activePage.sync="page" @update:activePage="getData()"
              :pages="total" />
          </CCardBody>
        </CCard>
      </transition>
    </CCol>
  </CRow>
</template>

<script>
import moment from "moment";
import VueTagsInput from "@johmun/vue-tags-input";
import AssignAccountLocation from "../../components/accounts/AssignAccountLocation.vue";

export default {
  name: "Accounts",
  components: {
    AssignAccountLocation,
    VueTagsInput,
  },

  data: () => {
    return {
      search: null,
      uploadPercentage: 0,
      progressBar: false,
      addOnKey: [13, 32, ":", ";"],
      items: [],
      fields: [
        "id",
        "name",
        "email",
        "type_name",
        "sub_type_name",
        "code",
        "active_date",
        "inactive_date",
        "actions",
      ],
      successModal: false,
      updateModal: false,
      sendModal: false,
      file: "",
      file_name: "",
      text: "",
      validation: [
        {
          classes: "email",
          rule: /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,24}))$/,
        },
      ],
      email: "",
      emails: [],
      checkedLastLevel: false,
      page: 1,
      total: 0,
      totalData: 0,
      previousTimeout: null,
    };
  },

  methods: {
    openAssignLocation(item) {
      this.$refs.assignAccountLocation.open(item)
    },
    format_date(value) {
      let date_format = localStorage.getItem("date_format");
      if (value) {
        return moment(String(value)).format(date_format);
      }
    },
    removeAccount(item) {
      const index = this.items.findIndex((type) => item.id == type.id);
      this.items.splice(index, 1);
    },
    deleteAccount(item) {
      axios
        .delete(`/api/accounts/${item.id}`)
        .then((res) => {
          this.removeAccount(item);
          this.flash("Account Deleted Successfully");
        })
        .catch((err) =>
          this.flash(
            "This Account connected to data on line or doctor or socials please check"
          )
        );
    },
    importAccount() {
      this.progressBar = true;
      let formData = new FormData();
      formData.append("file", this.file);
      this.importFile("/api/importaccounts", formData);
      document.getElementById("file").value = "";
      this.successModal = false;
    },

    importUpdateAccount() {
      this.progressBar = true;
      let formData = new FormData();
      formData.append("file", this.file);
      this.importFile("/api/importupdateaccounts", formData);
      document.getElementById("bulkEditFile").value = "";
      this.updateModal = false;
    },
    handleFileUpload(files, event) {
      this.file = event.target.files[0];
      this.file_name = event.target.files[0].name;
    },
    exportAccount() {
      this.exportFile("/api/exportaccounts", "Accounts.xlsx");
    },
    exportCSV() {
      this.exportFile("/api/exportaccountscsv", "Accounts.csv");
    },
    exportAccountPDF() {
      this.exportFile("/api/exportaccountpdf", "Accounts.pdf");
    },
    sendAccountMail() {
      const formData = {
        emails: JSON.stringify(this.emails),
        text: this.text,
      };
      this.sendMail("/api/sendmailaccounts", formData);
      this.successModal = false;
    },
    getData() {
      axios
        .post("/api/accounts/index?page=" + this.page, {
          query: this.search,
        })
        .then((res) => {
          this.items = res.data.data.data;
          this.total = res.data.data.last_page;
          this.totalData = res.data.data.total;
        })
        .catch((err) => {
          this.showErrorMessage(err);
        });
    },
    timeoutClear() {
      if (this.previousTimeout) {
        clearTimeout(this.previousTimeout);
        this.previousTimeout = null;
      }
    },
  },
  created() {
    this.getData();
  },
  watch: {
    search() {
      this.timeoutClear();
      const id = setTimeout(() => this.getData(), 500);
      this.previousTimeout = id;
    },
  },
};
</script>

<style>
.dd-item {
  display: flex;
  justify-content: space-between;
  width: 100%;
}

.dd-item-text {
  position: absolute;
  left: 3rem;
}
</style>
