<template>
  <c-col col="12" lg="12">
    <filter-data @getSchedule="filter" />

    <!-- Excel Processing Overlay -->
    <excel-processing-overlay
      :is-visible="isExcelProcessing"
      :progress="excelProgress"
      :message="excelProcessingMessage"
      :estimated-time="excelEstimatedTime"
      :memory-usage="excelMemoryUsage"
      :show-cancel-button="true"
      @cancel="cancelExcelProcessing"
    />

    <c-card v-if="items.length != 0">
      <c-card-header>
        <h3 class="text-center">
          {{ name }} <br />
          from: {{ saleData.fromDate }} to: {{ saleData.toDate }}
        </h3>
      </c-card-header>
      <c-card-body>
        <c-data-table ref="content" hover header tableFilter striped sorter footer :items="items" :fields="fields"
          :responsive="true" thead-top id="print">
          <template slot="thead-top">
            <td style="border-top: none"><strong>Total</strong></td>
            <td style="border-top: none" class="text-xs-right">
              {{ items.length }}
            </td>
          </template>
          <template #division="{ item }">
            <td>
              <v-chip v-if="item.division" :style="{ backgroundColor: item.color, color: 'white' }">
                {{ item.division }}
              </v-chip>
              <v-chip v-else>{{ item.division }}</v-chip>
            </td>
          </template>
          <template #employee="{ item }">
            <td>
              <v-chip v-if="item.employee" :style="{ backgroundColor: item.color, color: 'white' }">
                {{ item.employee }}
              </v-chip>
              <v-chip v-else></v-chip>
            </td>
          </template>
          <template #type="{ item }">
            <td>
              <v-chip :style="{ backgroundColor: colorfulType(item), color: 'white' }">
                {{ item.type }}
              </v-chip>
            </td>
          </template>
          <template #distributor="{ item }">
            <td>
              <v-chip :style="{ backgroundColor: colorfulDistributor(item), color: 'black' }">
                {{ item.distributor }}
              </v-chip>
            </td>
          </template>
          <template #reference="{ item }">
            <td>
              <v-chip v-if="item.referenceId" :style="{ backgroundColor: item.color, color: 'white' }" @click="showCeilingDetails(item)"
                style="cursor: pointer;">
                Show Ceiling Details
              </v-chip>
            </td>
          </template>
        </c-data-table>
      </c-card-body>
      <c-card-footer>
        <c-pagination v-if="items.length !== 0" :activePage.sync="page" @update:activePage="filter" :pages="total" />
        <download @getPrint="print" @getxlsx="download" @getpdf="createPDF" @getcsv="downloadCsv" :fields="fields"
          :data="items" :name="name" />
      </c-card-footer>
    </c-card>
    <sales-details-dialog ref="salesDetailsDialog" />
  </c-col>
</template>

<script>
import download from "../../components/download-reports/download.vue";
import filterData from "../../components/reports/salesdetails/filterData.vue";
import SalesDetailsDialog from "../../components/reports/SalesDetailsDialog.vue";
import ExcelProcessingOverlay from "../../components/common/ExcelProcessingOverlay.vue";
import { Amiri } from "../../assets/fonts/Amiri-Regular-normal";
import { capitalize } from "../../filters";
import jsPDF from "jspdf";
import "jspdf-autotable";

export default {
  components: {
    download,
    filterData,
    SalesDetailsDialog,
    ExcelProcessingOverlay,
  },
  data: () => {
    return {
      items: [],
      fields: [],
      saleData: {},
      name: "Unified Sales Details Report",
      page: 1,
      total: 0,
      salesDetails: null,

      // Excel processing state
      isExcelProcessing: false,
      excelProgress: 0,
      excelProcessingMessage: '',
      excelEstimatedTime: '',
      excelMemoryUsage: '',
      excelStartTime: null,
    };
  },
  emits: ["downloaded"],
  methods: {
    
    print() {
      this.$htmlToPaper("print");
    },
    async download() {
      try {
        // Show initial loading state
        this.isExcelProcessing = true;
        this.excelProgress = 0;
        this.excelProcessingMessage = 'Fetching data from server...';
        this.excelStartTime = Date.now();

        const response = await axios.post(`/api/unified-sales-details/download`, {
          saleFilter: this.saleData
        });

        this.excelData = response.data.data.list;
        this.dates = response.data.data.dates;

        // Check dataset size and warn user if very large
        const itemCount = this.excelData ? this.excelData.length : 0;
        if (itemCount > 100000) {
          const proceed = await this.confirmLargeDatasetDownload(itemCount);
          if (!proceed) {
            this.isExcelProcessing = false;
            return;
          }
        }

        // Update progress
        this.excelProgress = 10;
        this.excelProcessingMessage = `Processing ${itemCount.toLocaleString()} records...`;

        // Start Excel generation with enhanced error handling
        await this.downloadStyledExcel(
          this.dates,
          this.excelData,
          Object.keys(this.excelData[0] || {}),
          this.name,
          'Unified Sales Details Data:'
        );

      } catch (error) {
        this.isExcelProcessing = false;
        this.handleDownloadError(error);
      } finally {
        this.$emit("downloaded");
      }
    },

    async confirmLargeDatasetDownload(itemCount) {
      return new Promise((resolve) => {
        const message = `This dataset contains ${itemCount.toLocaleString()} records, which may take several minutes to process and could impact browser performance. Do you want to continue?`;

        if (confirm(message)) {
          resolve(true);
        } else {
          resolve(false);
        }
      });
    },

    handleDownloadError(error) {
      console.error('Download error:', error);

      if (error.response && error.response.status === 413) {
        this.showErrorMessage({
          response: {
            data: {
              message: 'Dataset too large for server processing. Please apply filters to reduce the data size.'
            }
          }
        });
      } else if (error.response && error.response.status === 504) {
        this.showErrorMessage({
          response: {
            data: {
              message: 'Server timeout while processing large dataset. Please try with a smaller date range or additional filters.'
            }
          }
        });
      } else {
        this.showErrorMessage(error);
      }
    },

    cancelExcelProcessing() {
      this.isExcelProcessing = false;
      this.excelProgress = 0;
      this.excelProcessingMessage = '';

      // If there's an active download, we can't really cancel it
      // but we can hide the UI and warn the user
      this.flash('Excel processing cancelled. If download was in progress, it may still complete in the background.', 'warning');
    },
    downloadCsv() {
      this.downloadXlsx(this.items, `${this.name}.csv`);
      this.$emit("downloaded");
    },
    createPDF() {
      let pdfName = this.name;
      const columns = this.fields.map((field) => {
        return {
          title: capitalize(field),
          dataKey: field,
        };
      });
      const body = this.items;
      const doc = new jsPDF({ filters: ["ASCIIHexEncode"] });

      doc.addFileToVFS("Amiri-Regular.ttf", Amiri);
      doc.addFont("Amiri-Regular.ttf", "Amiri", "normal");
      doc.setFont("Amiri");
      doc.setFontSize(10);
      doc.autoTable({
        columns,
        body,
        margin: { top: 10 },
        showHead: "firstPage",
        styles: {
          lineColor: "#c7c7c7",
          lineWidth: 0,
          cellPadding: 2,
          font: "Amiri",
        },
      });
      doc.save(pdfName + ".pdf");
    },
    filter({ saleFilter }) {
      if (saleFilter) {
        this.saleData = saleFilter
      }
      axios
        .post(`/api/unified-sales-details?page=${this.page}`, {
          saleFilter: this.saleData,
        })
        .then((response) => {
          this.items = response.data.data.data;
          this.fields = response.data.fields;
          this.total = response.data.data.last_page;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    colorfulType(item) {
      let types = this.items.map((obj) => obj.type);

      let uniqueTypesArray = types.reduce((accumulator, currentValue) => {
        if (!accumulator.includes(currentValue)) {
          accumulator.push(currentValue);
        }
        return accumulator;
      }, []);

      uniqueTypesArray = uniqueTypesArray.filter(function (element) {
        return element !== "";
      });

      const colorsArray = [
        "navy",
        "indigo",
        "yellowgreen",
        "teal",
        "steelblue",
        "seagreen",
        "purple",
      ];
      const myTypeHashTable = {};

      uniqueTypesArray.forEach((element, index) => {
        myTypeHashTable[element] = colorsArray[index]; // You can assign any value you want here
      });

      if (myTypeHashTable.hasOwnProperty(item.type)) {
        return myTypeHashTable[item.type];
      } else {
        return "transparent";
      }
    },
    colorfulDistributor(item) {
      let distributors = this.items.map((obj) => obj.distributor);

      let uniqueDistributorsArray = distributors.reduce((accumulator, currentValue) => {
        if (!accumulator.includes(currentValue)) {
          accumulator.push(currentValue);
        }
        return accumulator;
      }, []);

      uniqueDistributorsArray = uniqueDistributorsArray.filter(function (element) {
        return element !== "";
      });

      const colorsArray = [
        "aquamarine",
        "lightblue",
        "lightgreen",
        "lightcyan",
        "lavender",
        "mistyrose",
        "lavenderblush",
        "beige",
        "yellowgreen",
        "teal",
        "steelblue",
        "seagreen",
        "purple",
      ];
      const myDistributorHashTable = {};

      uniqueDistributorsArray.forEach((element, index) => {
        myDistributorHashTable[element] = colorsArray[index]; // You can assign any value you want here
      });

      if (myDistributorHashTable.hasOwnProperty(item.type)) {
        return myDistributorHashTable[item.type];
      } else {
        return "transparent";
      }
    },
    showCeilingDetails(item) {
      if (item.referenceId === null) return;
      axios.get(`/api/sales/${item.referenceId}/details`)
        .then(response => {
          this.$refs.salesDetailsDialog.open(response.data);
        })
        .catch(error => {
          this.showErrorMessage(error);
        });
    },
  },

  created() {
    // Listen for Excel processing events from the mixin
    this.$on('excel-processing-state', this.handleExcelProcessingState);
    this.$on('excel-progress', this.handleExcelProgress);
  },

  beforeDestroy() {
    // Clean up event listeners
    this.$off('excel-processing-state', this.handleExcelProcessingState);
    this.$off('excel-progress', this.handleExcelProgress);
  },

  methods: {
    handleExcelProcessingState({ isProcessing, message, startTime }) {
      this.isExcelProcessing = isProcessing;
      this.excelProcessingMessage = message;
      if (startTime) {
        this.excelStartTime = startTime;
      }
    },

    handleExcelProgress({ progress, message, estimatedTime, memoryUsage }) {
      this.excelProgress = progress;
      this.excelProcessingMessage = message;
      this.excelEstimatedTime = estimatedTime || '';
      this.excelMemoryUsage = memoryUsage || '';
    },

    print() {
      this.$htmlToPaper("print");
    },

    async download() {
      try {
        // Show initial loading state
        this.isExcelProcessing = true;
        this.excelProgress = 0;
        this.excelProcessingMessage = 'Fetching data from server...';
        this.excelStartTime = Date.now();

        const response = await axios.post(`/api/unified-sales-details/download`, {
          saleFilter: this.saleData
        });

        this.excelData = response.data.data.list;
        this.dates = response.data.data.dates;

        // Check dataset size and warn user if very large
        const itemCount = this.excelData ? this.excelData.length : 0;
        if (itemCount > 100000) {
          const proceed = await this.confirmLargeDatasetDownload(itemCount);
          if (!proceed) {
            this.isExcelProcessing = false;
            return;
          }
        }

        // Update progress
        this.excelProgress = 10;
        this.excelProcessingMessage = `Processing ${itemCount.toLocaleString()} records...`;

        // Start Excel generation with enhanced error handling
        await this.downloadStyledExcel(
          this.dates,
          this.excelData,
          Object.keys(this.excelData[0] || {}),
          this.name,
          'Unified Sales Details Data:'
        );

      } catch (error) {
        this.isExcelProcessing = false;
        this.handleDownloadError(error);
      } finally {
        this.$emit("downloaded");
      }
    },

    async confirmLargeDatasetDownload(itemCount) {
      return new Promise((resolve) => {
        const message = `This dataset contains ${itemCount.toLocaleString()} records, which may take several minutes to process and could impact browser performance. Do you want to continue?`;

        if (confirm(message)) {
          resolve(true);
        } else {
          resolve(false);
        }
      });
    },

    handleDownloadError(error) {
      console.error('Download error:', error);

      if (error.response && error.response.status === 413) {
        this.showErrorMessage({
          response: {
            data: {
              message: 'Dataset too large for server processing. Please apply filters to reduce the data size.'
            }
          }
        });
      } else if (error.response && error.response.status === 504) {
        this.showErrorMessage({
          response: {
            data: {
              message: 'Server timeout while processing large dataset. Please try with a smaller date range or additional filters.'
            }
          }
        });
      } else {
        this.showErrorMessage(error);
      }
    },

    cancelExcelProcessing() {
      this.isExcelProcessing = false;
      this.excelProgress = 0;
      this.excelProcessingMessage = '';

      // If there's an active download, we can't really cancel it
      // but we can hide the UI and warn the user
      this.flash('Excel processing cancelled. If download was in progress, it may still complete in the background.', 'warning');
    },

    downloadCsv() {
      this.downloadXlsx(this.items, `${this.name}.csv`);
      this.$emit("downloaded");
    },

    createPDF() {
      let pdfName = this.name;
      const columns = this.fields.map((field) => {
        return {
          title: capitalize(field),
          dataKey: field,
        };
      });
      const body = this.items;
      const doc = new jsPDF({ filters: ["ASCIIHexEncode"] });

      doc.addFileToVFS("Amiri-Regular.ttf", Amiri);
      doc.addFont("Amiri-Regular.ttf", "Amiri", "normal");
      doc.setFont("Amiri");
      doc.setFontSize(10);
      doc.autoTable({
        columns,
        body,
        margin: { top: 10 },
        showHead: "firstPage",
        styles: {
          lineColor: "#c7c7c7",
          lineWidth: 0,
          cellPadding: 2,
          font: "Amiri",
        },
      });
      doc.save(pdfName + ".pdf");
    },

    filter({ saleFilter }) {
      if (saleFilter) {
        this.saleData = saleFilter
      }
      axios
        .post(`/api/unified-sales-details?page=${this.page}`, {
          saleFilter: this.saleData,
        })
        .then((response) => {
          this.items = response.data.data.data;
          this.fields = response.data.fields;
          this.total = response.data.data.last_page;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },

    colorfulType(item) {
      let types = this.items.map((obj) => obj.type);

      let uniqueTypesArray = types.reduce((accumulator, currentValue) => {
        if (!accumulator.includes(currentValue)) {
          accumulator.push(currentValue);
        }
        return accumulator;
      }, []);

      uniqueTypesArray = uniqueTypesArray.filter(function (element) {
        return element !== "";
      });

      const colorsArray = [
        "navy",
        "indigo",
        "yellowgreen",
        "teal",
        "steelblue",
        "seagreen",
        "purple",
      ];
      const myTypeHashTable = {};

      uniqueTypesArray.forEach((element, index) => {
        myTypeHashTable[element] = colorsArray[index];
      });

      if (myTypeHashTable.hasOwnProperty(item.type)) {
        return myTypeHashTable[item.type];
      } else {
        return "transparent";
      }
    },

    colorfulDistributor(item) {
      let distributors = this.items.map((obj) => obj.distributor);

      let uniqueDistributorsArray = distributors.reduce((accumulator, currentValue) => {
        if (!accumulator.includes(currentValue)) {
          accumulator.push(currentValue);
        }
        return accumulator;
      }, []);

      uniqueDistributorsArray = uniqueDistributorsArray.filter(function (element) {
        return element !== "";
      });

      const colorsArray = [
        "aquamarine",
        "lightblue",
        "lightgreen",
        "lightcyan",
        "lavender",
        "mistyrose",
        "lavenderblush",
        "beige",
        "yellowgreen",
        "teal",
        "steelblue",
        "seagreen",
        "purple",
      ];
      const myDistributorHashTable = {};

      uniqueDistributorsArray.forEach((element, index) => {
        myDistributorHashTable[element] = colorsArray[index];
      });

      if (myDistributorHashTable.hasOwnProperty(item.type)) {
        return myDistributorHashTable[item.type];
      } else {
        return "transparent";
      }
    },

    showCeilingDetails(item) {
      if (item.referenceId === null) return;
      axios.get(`/api/sales/${item.referenceId}/details`)
        .then(response => {
          this.$refs.salesDetailsDialog.open(response.data);
        })
        .catch(error => {
          this.showErrorMessage(error);
        });
    },
  },
};
</script>
