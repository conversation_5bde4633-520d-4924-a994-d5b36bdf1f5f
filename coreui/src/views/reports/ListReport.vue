<template>
  <c-col col="12" lg="12">
    <filter-data @getSchedule="filter" />
    <c-card v-if="items.length != 0">
      <c-card-header>
        <div class="row">
          <div class="col-md-3"><strong>Accounts</strong></div>
          <div class="col-md-6">
            <h3 class="text-center">
              List Report for Line: {{ items[0].line }}
            </h3>
          </div>
          <div class="col-md-3">
            <CButton v-if="listData.action != 1 && listData.action != 4 && checkPermission('edit_accounts')"
              color="primary" @click="save" style="float: right">Save</CButton>
            <c-button color="danger" style="float: right" v-if="listData.action === 4" @click="
              $root
                .$confirm(
                  'Delete',
                  'If You Delete These Accounts, This will delete their Visits?',
                  {
                    color: 'red',
                    width: 290,
                    zIndex: 200,
                  }
                )
                .then((confirmed) => {
                  if (confirmed) {
                    save();
                  }
                })
              ">Save</c-button>
          </div>
        </div>
      </c-card-header>
      <c-card-body>
        <c-form-group>
          <template #input>
            <input class="m-1" v-if="listData.action != 1" id="checkAllAccounts" type="checkbox"
              v-model="checkAllAccounts" title="Check All Accounts" @change="checkAllAccount" />
            <label v-if="listData.action != 1" for="checkAllAccounts">Select All</label>
          </template>
        </c-form-group>
        <c-data-table ref="content" hover header tableFilter striped sorter footer :items="items" :fields="fields"
          :items-per-page="1000" :active-page="1" :responsive="true" pagination thead-top id="print">
          <template slot="thead-top">
            <td style="border-top: none"><strong>Total</strong></td>
            <td style="border-top: none" class="text-xs-right">
              {{ items.length }}
            </td>
          </template>
          <template #s="{ item }">
            <td>
              <input v-if="listData.action != 1" class="m-1" type="checkbox" v-model="selected" :value="item"
                title="Check One Account" />
            </td>
          </template>
          <!-- <template #division="{ item }">
            <td>
              <strong :style="{
                color: item.color,
              }">{{ item.division }}</strong>
            </td>
          </template> -->
          <template #line="{ item }">
            <td v-if="listData.action == 7">
              <p @click="showLineMore(item.id)" v-if="!readLineMore[item.id]"> {{ item.line.substring(0, 15) + ".." }}
                <span style="color: blue; cursor: pointer">show more</span>
              </p>
              <p @click="showLineLess(item.id)" v-if="readLineMore[item.id]">
                {{ item.line }} <br /> <span style="color: blue; cursor: pointer">show less</span> </p>
            </td>
            <td v-else>
              <strong>{{ item.line }}</strong>
            </td>
          </template>
          <template #division="{ item }">
            <td v-if="listData.action == 7">
              <p :style="{
                color: item.color,
              }" @click="showDivisionMore(item.id)" v-if="!readDivisionMore[item.id]"> {{ item.division.substring(0, 15) + ".." }}
                <span style="color: blue; cursor: pointer">show more</span>
              </p>
              <p @click="showDivisionLess(item.id)" v-if="readDivisionMore[item.id]">
                {{ item.division }} <br /> <span style="color: blue; cursor: pointer">show less</span> </p>
            </td>
            <td v-else>
              <strong :style="{
                color: item.color,
              }">{{ item.division }}</strong>
            </td>
          </template>
          <template #emp="{ item }">
            <td v-if="listData.action == 7 && item.emp != ''">
              <p :style="{
                color: item.color,
              }" @click="showEmpMore(item.id)" v-if="!readEmpMore[item.id]"> {{ item.emp.substring(0, 15) + ".." }}
                <span style="color: blue; cursor: pointer">show more</span>
              </p>
              <p @click="showEmpLess(item.id)" v-if="readEmpMore[item.id]">
                {{ item.emp }} <br /> <span style="color: blue; cursor: pointer">show less</span> </p>
            </td>
            <td v-else>
              <strong :style="{
                color: item.color,
              }">{{ item.emp }}</strong>
            </td>
          </template>
          <!-- <template #doctor="{ item }">
            <td>
              <strong style="color: blue; cursor: pointer"><a @click="$root.$doctor('Doctor Data', item.doctor_id)">{{
                item.doctor
                  }}</a></strong>
            </td>
          </template> -->
          <template #ucode="{ item }">
            <td v-if="listData.action == 7 && item.ucode != ''">
              <p @click="showUcodeMore(item.id)" v-if="!readUcodeMore[item.id]"> {{ item.ucode.substring(0, 15) + ".." }}
                <span style="color: blue; cursor: pointer">show more</span>
              </p>
              <p @click="showUcodeLess(item.id)" v-if="readUcodeMore[item.id]">
                {{ item.ucode }} <br /> <span style="color: blue; cursor: pointer">show less</span> </p>
            </td>
            <td v-else>
              <strong>{{ item.ucode }}</strong>
            </td>
          </template>
          <template #doctor="{ item }">
            <td v-if="listData.action == 7 && item.doctor != ''">
              <p @click="showDoctorMore(item.id)" v-if="!readDoctorMore[item.id]"> {{ item.doctor.substring(0, 15) + ".." }}
                <span style="color: blue; cursor: pointer">show more</span>
              </p>
              <p @click="showDoctorLess(item.id)" v-if="readDoctorMore[item.id]">
                {{ item.doctor }} <br /> <span style="color: blue; cursor: pointer">show less</span> </p>
            </td>
            <td v-else>
              <strong>{{ item.doctor }}</strong>
            </td>
          </template>
          <template #speciality="{ item }">
            <td v-if="listData.action == 7 && item.speciality != ''">
              <p @click="showSpecialityMore(item.id)" v-if="!readSpecialityMore[item.id]"> {{ item.speciality.substring(0, 15) + ".." }}
                <span style="color: blue; cursor: pointer">show more</span>
              </p>
              <p @click="showSpecialityLess(item.id)" v-if="readSpecialityMore[item.id]">
                {{ item.speciality }} <br /> <span style="color: blue; cursor: pointer">show less</span> </p>
            </td>
            <td v-else>
              <strong>{{ item.speciality }}</strong>
            </td>
          </template>
          <template #account="{ item }">
            <td>
              <strong style="color: blue; cursor: pointer"><a
                  @click="$root.$account('Account Data', item.account_id)">{{
                    item.account
                  }}</a></strong>
            </td>
          </template>
          <template #actions="{ item }">
            <td>
              <div class="row justify-content-center">
                <!-- <c-button
                  v-if="item.lat != 0 && item.lng != 0"
                  color="primary"
                  class="btn-sm mt-2 mr-1"
                  @click="
                    $root.$map(`Map of Account: ${item.name}`, item.location)
                  "
                  ><CIcon class="text-white" name="marker" />
                </c-button> -->
                <c-button color="primary" v-if="item.lat != 0 && item.lng != 0" class="btn-sm mt-2 mr-1"
                  @click="getLocation(item)">
                  <CIcon class="text-white" name="marker" />
                </c-button>
                <c-button color="warning" class="btn-sm mt-2 mr-1" v-if="checkPermission('reset_account_locations') &&
                  item.lat != null &&
                  item.lng != null
                " @click="resetLocation(item)"><c-icon name="cil-loop-circular" /></c-button>
                <c-button color="success" class="btn-sm mt-2 mr-1" v-if="checkPermission('edit_accounts')"
                  :to="{ name: 'EditAccount', params: { id: item.account_id } }"><c-icon name="cil-pencil" /></c-button>
              </div>
            </td>
          </template>
        </c-data-table>
      </c-card-body>
      <c-card-footer>
        <c-button color="primary"
          v-if="listData.action != 1 && listData.action != 5 && listData.action != 4 && checkPermission('edit_accounts')"
          @click="save" style="float: right">Save</c-button>
        <c-button color="primary"
          v-if="listData.action == 5 && checkPermission('reset_account_locations') && selected.length > 0" @click="save"
          style="float: right">Save</c-button>
        <c-button color="danger" style="float: right" v-if="listData.action === 4" @click="
          $root
            .$confirm(
              'Delete',
              'If You Delete These Accounts, This will delete their Visits?',
              {
                color: 'red',
                width: 290,
                zIndex: 200,
              }
            )
            .then((confirmed) => {
              if (confirmed) {
                save();
              }
            })
          ">Save</c-button>
        <download @getPrint="print" @getxlsx="download" @getpdf="createPDF" @getcsv="downloadCsv" :fields="fields"
          :data="items" :name="name" />
      </c-card-footer>
    </c-card>
  </c-col>
</template>

<script>
import download from "../../components/download-reports/download.vue";
import filterData from "../../components/reports/list/filterData.vue";
import { Amiri } from "../../assets/fonts/Amiri-Regular-normal";
import jsPDF from "jspdf";
import "jspdf-autotable";
import { capitalize } from "../../filters";
export default {
  components: {
    download,
    filterData,
    capitalize,
  },
  data: () => {
    return {
      items: [],
      fields: [],
      checkAllAccounts: false,
      name: "List Report",
      listData: {},
      selected: [],
      dates: [],
      readLineMore: {},
      readDivisionMore: {},
      readEmpMore: {},
      readUcodeMore: {},
      readDoctorMore: {},
      readSpecialityMore: {},
    };
  },
  emits: ["downloaded"],
  methods: {
    showLineMore(id) {
      this.$set(this.readLineMore, id, true);
    },
    showLineLess(id) {
      this.$set(this.readLineMore, id, false);
    },
    showDivisionMore(id) {
      this.$set(this.readDivisionMore, id, true);
    },
    showDivisionLess(id) {
      this.$set(this.readDivisionMore, id, false);
    },
    showEmpMore(id) {
      this.$set(this.readEmpMore, id, true);
    },
    showEmpLess(id) {
      this.$set(this.readEmpMore, id, false);
    },
    showUcodeMore(id) {
      this.$set(this.readUcodeMore, id, true);
    },
    showUcodeLess(id) {
      this.$set(this.readUcodeMore, id, false);
    },
    showDoctorMore(id) {
      this.$set(this.readDoctorMore, id, true);
    },
    showDoctorLess(id) {
      this.$set(this.readDoctorMore, id, false);
    },
    showSpecialityMore(id) {
      this.$set(this.readSpecialityMore, id, true);
    },
    showSpecialityLess(id) {
      this.$set(this.readSpecialityMore, id, false);
    },
    checkAllAccount() {
      this.selected = [];
      if (this.checkAllAccounts) {
        for (let i in this.items) {
          this.selected.push(this.items[i]);
        }
      }
    },
    getLocation(account) {
      axios
        .post("/api/account-location", {
          account,
        })
        .then((response) => {
          this.location = response.data.data;
          if (this.location.lat != 0 && this.location.lng != 0) {
            this.$root.$map(`Map of Account id: ${this.location.account_id}`, [
              this.location,
            ]);
          } else {
            this.flash("There is no loaction for this visit");
          }
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    resetLocation(item) {
      axios
        .post(`/api/reset-location`, {
          item,
        })
        .then((response) => {
          this.flash(`Account ${item.account} reset the location`);
          axios
            .post(`/api/list-report`, {
              listFilter: this.listData,
            })
            .then((response) => {
              this.items = response.data.data;
            })
            .catch((error) => {
              this.showErrorMessage(error);
            });
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    download() {
      this.downloadStyledExcel(
        this.dates,
        this.items,
        Object.keys(this.items[0]),
        'List Report',
        'Employee List Data:');
    },
    downloadCsv() {
      let filteredAccounts = Object.values(this.items);
      filteredAccounts.forEach((element) => {
        delete element["color"];
      });
      this.downloadXlsx(filteredAccounts, "List.csv");
      this.$emit("downloaded");
    },
    print() {
      this.$htmlToPaper("print");
    },
    save() {
      let data = this.selected.map((account) => {
        return {
          account_id: account.account_id,
          line_id: account.line_id,
          div_id: account.div_id,
          brick_id: account.brick_id,
          doctor_id: account.doctor_id,
        }
      });
      axios
        .post(`/api/list-report-action`, {
          action: this.listData.action,
          accounts: data,
        })
        .then((response) => {
          this.flash("Action Saved Successfully");
          this.selected = [];
          this.checkAllAccounts = false;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    createPDF() {
      let pdfName = this.name;
      var columns = this.fields.map((field) => {
        return {
          title: capitalize(field),
          dataKey: field,
        };
      });
      const body = this.items;
      const doc = new jsPDF({ filters: ["ASCIIHexEncode"] });

      doc.addFileToVFS("Amiri-Regular.ttf", Amiri);
      doc.addFont("Amiri-Regular.ttf", "Amiri", "normal");
      doc.setFont("Amiri");
      doc.setFontSize(10);
      doc.autoTable({
        columns,
        body,
        margin: { top: 10 },
        showHead: "firstPage",
        styles: {
          lineColor: "#c7c7c7",
          lineWidth: 0,
          cellPadding: 2,
          font: "Amiri",
        },
      });
      doc.save(pdfName + ".pdf");
    },
    filter({ listFilter }) {
      this.listData = listFilter;
      axios
        .post(`/api/list-report`, {
          listFilter,
        })
        .then((response) => {
          this.items = response.data.data.accounts;
          this.dates = response.data.data.dates;
          this.fields = response.data.data.fields;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
  },
};
</script>