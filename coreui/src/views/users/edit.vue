<template>
  <CRow>
    <CCol col="12" lg="12">
      <CCard no-header>
        <CCardBody>
          <CForm>
            <template slot="header">
              Edit User id: {{ $route.params.id }}
            </template>
            <CTabs>
              <CTab active>
                <template slot="title">
                  <CIcon class="custom_icon" name="cil-chart-pie" /> Basic Info
                </template>
                <CCard no-header>
                  <CCardBody>
                    <div class="row">
                      <div class="col">
                        <CInput label="User Name" type="text" placeholder="User Name" v-model="user.name"></CInput>
                      </div>
                      <div class="col">
                        <CInput label="Full Name" type="text" placeholder="Full Name" v-model="user.fullname"></CInput>
                      </div>
                    </div>

                    <div class="row">
                      <div class="col">
                        <CFormGroup>
                          <template #label> Scientific Office </template>
                          <template #input>
                            <v-select label="name" v-model="user.office_id" :options="offices"
                              placeholder="Select Office" :reduce="(office) => office.id" class="mt-2" />
                          </template>
                        </CFormGroup>
                      </div>
                      <div class="col">
                        <CInput label="Work Email" placeholder="Work Email" v-model="user.email"></CInput>
                      </div>
                      <div class="col">
                        <CInput label="Personal Email" type="email" placeholder="Personal Email"
                          v-model="user.personal_email">
                        </CInput>
                      </div>
                    </div>

                    <div class="row">
                      <div class="col">
                        <CFormGroup>
                          <template #label> Status </template>
                          <template #input>
                            <v-select title="Status" v-model="user.status" :options="status" placeholder="Select option"
                              :reduce="(status) => status.value" class="mt-2" />
                          </template>
                        </CFormGroup>
                      </div>
                      <div class="col">
                        <CFormGroup>
                          <template #label> Role </template>
                          <template #input>
                            <v-select v-model="user.menuroles" multiple :options="roles" placeholder="Select Role"
                              class="mt-2" />
                          </template>
                        </CFormGroup>
                      </div>
                    </div>

                    <div class="row">
                      <div class="col">
                        <CInput label="Password" placeholder="Password" v-model="user.password"></CInput>
                      </div>
                      <div class="col">
                        <CInputFile ref="imageInput" type="file" accept="image/*" @change="onImageChange"
                          label="Profile Picture" />
                        <img v-if="url" :src="url" name="image" width="70px" height="70px" />
                      </div>
                    </div>
                    <div class="row">
                      <div class="col-4">
                        <c-form-group>
                          <template #label>
                            <strong>Hiring Date</strong>
                          </template>
                          <template #input>
                            <c-input type="date" class="mt-2" placeholder="Date" v-model="user.hiringDate"></c-input>
                          </template>
                        </c-form-group>
                      </div>
                      <div class="col-4">
                        <c-form-group>
                          <template #label>
                            <strong>Employee Code</strong>
                          </template>
                          <template #input>
                            <c-input type="text" class="mt-2" placeholder="Code" v-model="user.emp_code"></c-input>
                          </template>
                        </c-form-group>
                      </div>
                      <div class="col-4">
                        <c-form-group>
                          <template #label>
                            <strong>Serial</strong>
                          </template>
                          <template #input>
                            <c-input type="text" class="mt-2" placeholder="Serial" v-model="user.serial"></c-input>
                          </template>
                        </c-form-group>
                      </div>
                      <div class="col-4">
                        <label>Is Vacant</label><br />
                        <input type="checkbox" :id="`is_vacant`" v-model="user.is_vacant" :checked="checkIsVacant()"
                          @click="setIsVacant()" />
                      </div>
                    </div>
                  </CCardBody>
                  <CCardFooter>
                    <CButton color="primary" @click="update()">Update</CButton>
                    <CButton color="default" @click="$router.go(-1)">Cancel</CButton>
                  </CCardFooter>
                </CCard>
              </CTab>

              <CTab>
                <template slot="title">
                  <CIcon class="custom_icon" name="cil-chart-pie" /> Details
                </template>
                <CCard no-header>
                  <CCardBody>
                    <div class="row">
                      <div class="col">
                        <CInput label="Socila Security Number" type="text" placeholder="Social Security Number"
                          v-model="userDetails.ssn"></CInput>
                      </div>
                      <div class="col">
                        <CInput label="Date Of Birth" type="date" placeholder="Date Of Birth" v-model="userDetails.dob">
                        </CInput>
                      </div>
                    </div>

                    <div class="row">
                      <!-- <div class="col">
                      <CInput label="Land Line" type="textarea" placeholder="Land Line" v-model="userDetails.tel"></CInput>
                    </div> -->
                      <div class="col">
                        <CFormGroup wrapperClasses="input-group pt-2" description="ex. 02-99999999">
                          <template #prepend-content>
                            <CIcon name="cil-phone" />
                          </template>
                          <template #label> Tel. </template>
                          <template #input>
                            <masked-input type="tel" name="tel" class="form-control" v-model="userDetails.tel" :mask="[
                              0,
                              2,
                              '-',
                              /\d/,
                              /\d/,
                              /\d/,
                              /\d/,
                              /\d/,
                              /\d/,
                              /\d/,
                              /\d/,
                            ]" :guide="true" placeholderChar="#" />
                          </template>
                        </CFormGroup>
                      </div>
                      <!-- <div class="col">
                      <CInput label="Mobile" type="text" placeholder="Mobile" v-model="userDetails.mobile"></CInput>
                    </div> -->
                      <div class="col">
                        <CFormGroup wrapperClasses="input-group pt-2" description="ex. 0 (*************">
                          <template #prepend-content>
                            <CIcon name="cil-phone" />
                          </template>
                          <template #label> Mobile </template>
                          <template #input>
                            <masked-input type="mobile" name="mobile" class="form-control" v-model="userDetails.mobile"
                              :mask="[
                                '0',
                                /[1-9]/,
                                /\d/,
                                /\d/,
                                /\d/,
                                /\d/,
                                /\d/,
                                /\d/,
                                /\d/,
                                /\d/,
                                /\d/,
                              ]" :guide="true" placeholderChar="#" />
                          </template>
                        </CFormGroup>
                      </div>
                    </div>

                    <div class="row">
                      <div class="col">
                        <CTextarea label="Address" type="text" placeholder="Address" v-model="userDetails.address">
                        </CTextarea>
                      </div>
                      <div class="col">
                        <CFormGroup>
                          <template #label> Gender </template>
                          <template #input>
                            <v-select title="Gender" v-model="userDetails.gender" :options="genders"
                              placeholder="Select option" :reduce="(gender) => gender.value" class="mt-2" />
                          </template>
                        </CFormGroup>
                      </div>
                    </div>
                  </CCardBody>
                  <CCardFooter>
                    <CButton color="primary" @click="storeDetails()" v-if="userDetails.id == 0">Save</CButton>
                    <CButton color="primary" @click="updateDetails()" v-if="userDetails.id !== 0">Update</CButton>
                    <CButton color="default" @click="$router.go(-1)">Cancel</CButton>
                  </CCardFooter>
                </CCard>
              </CTab>

              <CTab>
                <template slot="title">
                  <CIcon class="custom_icon" name="cil-chart-pie" /> Line & Division
                </template>
                <CCard no-header>
                  <CCardBody>
                    <line-division-tab :user_id="userDetails.user_id" :user="userLineDivision" />
                  </CCardBody>

                </CCard>
              </CTab>

            </CTabs>
          </CForm>
        </CCardBody>
      </CCard>
    </CCol>
  </CRow>
</template>

<script>
import MaskedInput from "vue-text-mask";
import moment from "moment";
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
import LineDivisionTab from "../../components/users/filterData.vue";

export default {
  name: "EditUser",
  components: {
    LineDivisionTab,
    MaskedInput,
    vSelect,
  },
  data: () => {
    return {
      user: {
        id: 0,
        name: "",
        fullname: "",
        email: null,
        personal_email: "",
        password: "",
        status: "",
        menuroles: "",
        office_id: null,
        serial: null,
        hiringDate: null,
        is_vacant: 0,
        emp_code: null,
        image: localStorage.getItem("user_image"),
      },
      userDetails: {
        id: 0,
        user_id: "",
        dob: "",
        ssn: "",
        mobile: "",
        tel: "",
        address: "",
        gender: "",
      },
      userLineDivision: [],
      url: null,
      path: null,
      status: [
        { value: "Active", label: "Active" },
        { value: "Inactive", label: "Inactive" },
      ],
      genders: [
        { value: "male", label: "Male" },
        { value: "female", label: "Female" },
      ],
      roles: [],
      offices: [],
      image_name: null,
    };
  },
  methods: {
    initialize() {
      axios
        .get(`/api/users/${this.$route.params.id}/edit`)
        .then((response) => {
          this.offices = response.data.offices;
          this.user.id = response.data.user.id;
          this.url = response.data.url;
          this.user.name = response.data.user.name;
          this.user.fullname = response.data.user.fullname;
          this.user.menuroles = response.data.user.menuroles;
          this.user.password = response.data.user.password;
          this.user.serial = response.data.user.android_id;
          this.user.menuroles = this.user.menuroles.split(",");
          this.user.status = response.data.user.status;
          this.user.hiringDate = response.data.user.hiring_date;
          this.user.emp_code = response.data.user.emp_code;
          this.user.office_id = response.data.user.office_id;
          this.user.is_vacant = response.data.user.is_vacant;
          this.user.email = response.data.user.email;
          this.user.personal_email = response.data.user.personal_email;
          this.user.image = response.data.user.image;
          this.userLineDivision = response.data.userLineDivision;
          this.roles = Object.keys(response.data.roles).map((key) => {
            return response.data.roles[key];
          });
          if (response.data.userDetails !== null) {
            this.userDetails = response.data.userDetails;
            this.userDetails.dob = this.edit_date_format(
              response.data.userDetails.dob
            );
          } else {
            this.userDetails.user_id = response.data.user.id;
          }
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    // onImageChange(files, event) {
    //   this.user.image = event.target.files[0];
    //   this.image_name = event.target.files[0].name;
    // },
    onImageChange(files, event) {
      const file = event.target.files[0];
      const formData = new FormData();
      formData.append("image", file);
      formData.append("folder", `users/${this.user.id}/profile`);
      axios
        .post("/api/upload/images", formData)
        .then((response) => {
          this.url = response.data.data.url;
          this.path = response.data.data.path;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    setIsVacant() {
      if (document.getElementById("is_vacant").checked) {
        this.user.is_vacant = 1;
      } else {
        this.user.is_vacant = 0;
      }
    },
    checkIsVacant() {
      if (this.user.is_vacant == 1) {
        return true;
      }
      return false;
    },
    format_date(value) {
      let date_format = localStorage.getItem("date_format");
      if (value) {
        return moment(String(value)).format(date_format);
      }
    },
    edit_date_format(value) {
      if (value) {
        return moment(String(value)).format("YYYY-MM-DD");
      }
    },
    update() {
      let request = {
        id: this.user.id,
        name: this.user.name,
        fullname: this.user.fullname,
        email: this.user.email,
        serial: this.user.serial,
        image: this.path,
        personal_email: this.user.personal_email,
        status: this.user.status,
        office_id: this.user.office_id,
        is_vacant: this.user.is_vacant,
        hiringDate: this.user.hiringDate,
        empCode: this.user.emp_code,
        menuroles: this.user.menuroles,
        password: this.user.password,
      };
      let formData = new FormData();
      formData.append("image", this.user.image);
      axios
        .put(`/api/users/${this.$route.params.id}`, request)
        .then((response) => {
          this.userDetails.user_id = response.data.user_id;
          axios
            .put(`/api/users/image/${this.$route.params.id}`, formData, {
              headers: {
                "content-type": "multipart/form-data",
              },
            })
            .then((response) => {
              this.flash("User Updated Successfully");
            })
            .catch((error) => {
              this.showErrorMessage(error);
            });
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    updateDetails() {
      axios
        .put(`/api/user/details/${this.userDetails.id}`, {
          user_id: this.userDetails.user_id,
          dob: this.userDetails.dob,
          ssn: this.userDetails.ssn,
          mobile: this.userDetails.mobile,
          tel: this.userDetails.tel,
          address: this.userDetails.address,
          gender: this.userDetails.gender,
        })
        .then((response) => {
          this.flash("User Details Updated Successfully");
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    storeDetails() {
      axios
        .post("/api/user/details", {
          user_id: this.userDetails.user_id,
          dob: this.userDetails.dob,
          ssn: this.userDetails.ssn,
          mobile: this.userDetails.mobile,
          tel: this.userDetails.tel,
          address: this.userDetails.address,
          gender: this.userDetails.gender,
        })
        .then((response) => {
          this.userDetails = {
            user_id: 0,
            dob: "",
            ssn: "",
            mobile: "",
            tel: "",
            address: "",
            gender: "",
          };
          this.flash("User Details Created Successfully");
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
  },
  created() {
    this.initialize();
  },
};
</script>
